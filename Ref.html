<html lang="en" style="font-size: 16px;">

<head>
    <style>
        .ͼ1.cm-focused {
            outline: 1px dotted #212121;
        }

        .ͼ1 {
            position: relative !important;
            box-sizing: border-box;
            display: flex !important;
            flex-direction: column;
        }

        .ͼ1 .cm-scroller {
            display: flex !important;
            align-items: flex-start !important;
            font-family: monospace;
            line-height: 1.4;
            height: 100%;
            overflow-x: auto;
            position: relative;
            z-index: 0;
            overflow-anchor: none;
        }

        .ͼ1 .cm-content[contenteditable=true] {
            -webkit-user-modify: read-write-plaintext-only;
        }

        .ͼ1 .cm-content {
            margin: 0;
            flex-grow: 2;
            flex-shrink: 0;
            display: block;
            white-space: pre;
            word-wrap: normal;
            box-sizing: border-box;
            min-height: 100%;
            padding: 4px 0;
            outline: none;
        }

        .ͼ1 .cm-lineWrapping {
            white-space: pre-wrap;
            white-space: break-spaces;
            word-break: break-word;
            overflow-wrap: anywhere;
            flex-shrink: 1;
        }

        .ͼ2 .cm-content {
            caret-color: black;
        }

        .ͼ3 .cm-content {
            caret-color: white;
        }

        .ͼ1 .cm-line {
            display: block;
            padding: 0 2px 0 6px;
        }

        .ͼ1 .cm-layer>* {
            position: absolute;
        }

        .ͼ1 .cm-layer {
            position: absolute;
            left: 0;
            top: 0;
            contain: size style;
        }

        .ͼ2 .cm-selectionBackground {
            background: #d9d9d9;
        }

        .ͼ3 .cm-selectionBackground {
            background: #222;
        }

        .ͼ2.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground {
            background: #d7d4f0;
        }

        .ͼ3.cm-focused>.cm-scroller>.cm-selectionLayer .cm-selectionBackground {
            background: #233;
        }

        .ͼ1 .cm-cursorLayer {
            pointer-events: none;
        }

        .ͼ1.cm-focused>.cm-scroller>.cm-cursorLayer {
            animation: steps(1) cm-blink 1.2s infinite;
        }

        @keyframes cm-blink {
            50% {
                opacity: 0;
            }
        }

        @keyframes cm-blink2 {
            50% {
                opacity: 0;
            }
        }

        .ͼ1 .cm-cursor,
        .ͼ1 .cm-dropCursor {
            border-left: 1.2px solid black;
            margin-left: -0.6px;
            pointer-events: none;
        }

        .ͼ1 .cm-cursor {
            display: none;
        }

        .ͼ3 .cm-cursor {
            border-left-color: #ddd;
        }

        .ͼ1 .cm-dropCursor {
            position: absolute;
        }

        .ͼ1.cm-focused>.cm-scroller>.cm-cursorLayer .cm-cursor {
            display: block;
        }

        .ͼ1 .cm-iso {
            unicode-bidi: isolate;
        }

        .ͼ1 .cm-announced {
            position: fixed;
            top: -10000px;
        }

        @media print {
            .ͼ1 .cm-announced {
                display: none;
            }
        }

        .ͼ2 .cm-activeLine {
            background-color: #cceeff44;
        }

        .ͼ3 .cm-activeLine {
            background-color: #99eeff33;
        }

        .ͼ2 .cm-specialChar {
            color: red;
        }

        .ͼ3 .cm-specialChar {
            color: #f78;
        }

        .ͼ1 .cm-gutters {
            flex-shrink: 0;
            display: flex;
            height: 100%;
            box-sizing: border-box;
            inset-inline-start: 0;
            z-index: 200;
        }

        .ͼ2 .cm-gutters {
            background-color: #f5f5f5;
            color: #6c6c6c;
            border-right: 1px solid #ddd;
        }

        .ͼ3 .cm-gutters {
            background-color: #333338;
            color: #ccc;
        }

        .ͼ1 .cm-gutter {
            display: flex !important;
            flex-direction: column;
            flex-shrink: 0;
            box-sizing: border-box;
            min-height: 100%;
            overflow: hidden;
        }

        .ͼ1 .cm-gutterElement {
            box-sizing: border-box;
        }

        .ͼ1 .cm-lineNumbers .cm-gutterElement {
            padding: 0 3px 0 5px;
            min-width: 20px;
            text-align: right;
            white-space: nowrap;
        }

        .ͼ2 .cm-activeLineGutter {
            background-color: #e2f2ff;
        }

        .ͼ3 .cm-activeLineGutter {
            background-color: #222227;
        }

        .ͼ1 .cm-panels {
            box-sizing: border-box;
            position: sticky;
            left: 0;
            right: 0;
            z-index: 300;
        }

        .ͼ2 .cm-panels {
            background-color: #f5f5f5;
            color: black;
        }

        .ͼ2 .cm-panels-top {
            border-bottom: 1px solid #ddd;
        }

        .ͼ2 .cm-panels-bottom {
            border-top: 1px solid #ddd;
        }

        .ͼ3 .cm-panels {
            background-color: #333338;
            color: white;
        }

        .ͼ1 .cm-tab {
            display: inline-block;
            overflow: hidden;
            vertical-align: bottom;
        }

        .ͼ1 .cm-widgetBuffer {
            vertical-align: text-top;
            height: 1em;
            width: 0;
            display: inline;
        }

        .ͼ1 .cm-placeholder {
            color: #888;
            display: inline-block;
            vertical-align: top;
        }

        .ͼ1 .cm-highlightSpace {
            background-image: radial-gradient(circle at 50% 55%, #aaa 20%, transparent 5%);
            background-position: center;
        }

        .ͼ1 .cm-highlightTab {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="20"><path stroke="%23888" stroke-width="1" fill="none" d="M1 10H196L190 5M190 15L196 10M197 4L197 16"/></svg>');
            background-size: auto 100%;
            background-position: right 90%;
            background-repeat: no-repeat;
        }

        .ͼ1 .cm-trailingSpace {
            background-color: #ff332255;
        }

        .ͼ1 .cm-button {
            vertical-align: middle;
            color: inherit;
            font-size: 70%;
            padding: .2em 1em;
            border-radius: 1px;
        }

        .ͼ2 .cm-button:active {
            background-image: linear-gradient(#b4b4b4, #d0d3d6);
        }

        .ͼ2 .cm-button {
            background-image: linear-gradient(#eff1f5, #d9d9df);
            border: 1px solid #888;
        }

        .ͼ3 .cm-button:active {
            background-image: linear-gradient(#111, #333);
        }

        .ͼ3 .cm-button {
            background-image: linear-gradient(#393939, #111);
            border: 1px solid #888;
        }

        .ͼ1 .cm-textfield {
            vertical-align: middle;
            color: inherit;
            font-size: 70%;
            border: 1px solid silver;
            padding: .2em .5em;
        }

        .ͼ2 .cm-textfield {
            background-color: white;
        }

        .ͼ3 .cm-textfield {
            border: 1px solid #555;
            background-color: inherit;
        }

        .ͼ1 .cm-foldPlaceholder {
            background-color: #eee;
            border: 1px solid #ddd;
            color: #888;
            border-radius: .2em;
            margin: 0 1px;
            padding: 0 1px;
            cursor: pointer;
        }

        .ͼ1 .cm-foldGutter span {
            padding: 0 1px;
            cursor: pointer;
        }
    </style>
    <meta charset="utf-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta http-equiv="Content-Security-Policy" content="style-src 'unsafe-inline' 'self' https://fonts.googleapis.com">
    <title>Scope - Karan's Notes - Obsidian v1.8.10</title>
    <link href="app.css" type="text/css" rel="stylesheet">
    <style type="text/css"></style>
</head>

<body
    class="mod-macos is-frameless is-maximized is-hidden-frameless obsidian-app native-scrollbars theme-light show-inline-title show-ribbon show-view-header is-focused"
    style="--zoom-factor: 1; --font-text-size: 16px; --indent-size: 4;">
    <div class="titlebar">
        <div class="titlebar-inner">
            <div class="titlebar-text">Scope - Karan's Notes - Obsidian v1.8.10</div>
            <div class="titlebar-button-container mod-left"></div>
            <div class="titlebar-button-container mod-right"></div>
        </div>
    </div>
    <script type="text/javascript" defer="" src="lib/codemirror/codemirror.js"></script>
    <script type="text/javascript" defer="" src="lib/codemirror/overlay.js"></script>
    <script type="text/javascript" defer="" src="lib/codemirror/markdown.js"></script>
    <script type="text/javascript" defer="" src="lib/codemirror/cm-addons.js"></script>
    <script type="text/javascript" defer="" src="lib/codemirror/vim.js"></script>
    <script type="text/javascript" defer="" src="lib/codemirror/meta.min.js"></script>
    <script type="text/javascript" defer="" src="lib/moment.min.js"></script>
    <script type="text/javascript" defer="" src="lib/pixi.min.js"></script>
    <script type="text/javascript" defer="" src="lib/i18next.min.js"></script>
    <script type="text/javascript" defer="" src="lib/scrypt.js"></script>
    <script type="text/javascript" defer="" src="lib/turndown.js"></script>
    <script type="text/javascript" defer="" src="enhance.js"></script>
    <script type="text/javascript" defer="" src="i18n.js"></script>
    <script type="text/javascript" defer="" src="app.js"></script>


    <div class="app-container">
        <div class="horizontal-main-container">
            <div class="workspace is-left-sidedock-open is-right-sidedock-open">
                <div class="workspace-ribbon side-dock-ribbon mod-left">
                    <div class="side-dock-actions">
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Open quick switcher"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-file-search">
                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                <path d="M4.268 21a2 2 0 0 0 1.727 1H18a2 2 0 0 0 2-2V7l-5-5H6a2 2 0 0 0-2 2v3"></path>
                                <path d="m9 18-1.5-1.5"></path>
                                <circle cx="5" cy="14" r="3"></circle>
                            </svg></div>
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Open graph view"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-git-fork">
                                <circle cx="12" cy="18" r="3"></circle>
                                <circle cx="6" cy="6" r="3"></circle>
                                <circle cx="18" cy="6" r="3"></circle>
                                <path d="M18 9v2c0 .6-.4 1-1 1H7c-.6 0-1-.4-1-1V9"></path>
                                <path d="M12 12v3"></path>
                            </svg></div>
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Create new canvas"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-layout-dashboard">
                                <rect x="3" y="3" width="7" height="9" rx="1"></rect>
                                <rect x="14" y="3" width="7" height="5" rx="1"></rect>
                                <rect x="14" y="12" width="7" height="9" rx="1"></rect>
                                <rect x="3" y="16" width="7" height="5" rx="1"></rect>
                            </svg></div>
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Open today's daily note"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-calendar">
                                <path d="M8 2v4"></path>
                                <path d="M16 2v4"></path>
                                <rect x="3" y="4" width="18" height="18" rx="2"></rect>
                                <path d="M3 10h18"></path>
                            </svg></div>
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Insert template"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-files">
                                <path d="M20 7h-3a2 2 0 0 1-2-2V2"></path>
                                <path d="M9 18a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h7l4 4v10a2 2 0 0 1-2 2Z"></path>
                                <path d="M3 7.6v12.8A1.6 1.6 0 0 0 4.6 22h9.8"></path>
                            </svg></div>
                        <div class="clickable-icon side-dock-ribbon-action" aria-label="Open command palette"
                            data-tooltip-position="right" data-tooltip-delay="300"><svg
                                xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                stroke-linejoin="round" class="svg-icon lucide-terminal">
                                <polyline points="4 17 10 11 4 5"></polyline>
                                <line x1="12" y1="19" x2="20" y2="19"></line>
                            </svg></div>
                    </div>
                    <div class="side-dock-settings"></div>
                </div>
                <div class="workspace-split mod-horizontal mod-sidedock mod-left-split" style="width: 350.5px;">
                    <hr class="workspace-leaf-resize-handle">
                    <div class="workspace-sidedock-vault-profile">
                        <div class="workspace-drawer-vault-switcher">
                            <div class="workspace-drawer-vault-switcher-icon"><svg xmlns="http://www.w3.org/2000/svg"
                                    width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="svg-icon lucide-chevrons-up-down">
                                    <path d="m7 15 5 5 5-5"></path>
                                    <path d="m7 9 5-5 5 5"></path>
                                </svg></div>
                            <div class="workspace-drawer-vault-name">Karan's Notes</div>
                        </div>
                        <div class="workspace-drawer-vault-actions"><span class="clickable-icon"><svg
                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                    fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                    stroke-linejoin="round" class="svg-icon help">
                                    <path
                                        d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z">
                                    </path>
                                    <path
                                        d="M9.09009 9.00003C9.32519 8.33169 9.78924 7.76813 10.4 7.40916C11.0108 7.05019 12.079 6.94542 12.7773 7.06519C13.9093 7.25935 14.9767 8.25497 14.9748 9.49073C14.9748 11.9908 12 11.2974 12 14">
                                    </path>
                                    <path d="M12 17H12.01"></path>
                                </svg></span><span class="clickable-icon"><svg xmlns="http://www.w3.org/2000/svg"
                                    width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                    class="svg-icon lucide-settings">
                                    <path
                                        d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z">
                                    </path>
                                    <circle cx="12" cy="12" r="3"></circle>
                                </svg></span></div>
                    </div>
                    <div class="workspace-sidedock-empty-state" style="display: none;">
                        <p class="u-muted">The sidebar is empty, try dragging a tab here.</p>
                    </div>
                    <div class="workspace-tabs mod-top mod-top-left-space">
                        <hr class="workspace-leaf-resize-handle">
                        <div class="workspace-tab-header-container">
                            <div class="workspace-tab-header-container-inner" style="--animation-dur: 250ms;">
                                <div class="workspace-tab-header tappable is-active" draggable="true" aria-label="Files"
                                    data-tooltip-delay="300" data-type="file-explorer">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-folder-closed">
                                                <path
                                                    d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z">
                                                </path>
                                                <path d="M2 10h20"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Files</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Search"
                                    data-tooltip-delay="300" data-type="search">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-search">
                                                <circle cx="11" cy="11" r="8"></circle>
                                                <path d="m21 21-4.3-4.3"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Search</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Bookmarks"
                                    data-tooltip-delay="300" data-type="bookmarks">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-bookmark">
                                                <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Bookmarks</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                            </div>
                            <div class="workspace-tab-header-new-tab"><span class="clickable-icon"
                                    aria-label="New tab"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-plus">
                                        <path d="M5 12h14"></path>
                                        <path d="M12 5v14"></path>
                                    </svg></span></div>
                            <div class="workspace-tab-header-spacer"></div>
                            <div class="workspace-tab-header-tab-list"><span class="clickable-icon"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="svg-icon lucide-chevron-down">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></span></div>
                            <div class="sidebar-toggle-button mod-left" aria-label="" data-tooltip-position="right">
                                <div class="clickable-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="svg-icon sidebar-left">
                                        <path
                                            d="M21 3H3C1.89543 3 1 3.89543 1 5V19C1 20.1046 1.89543 21 3 21H21C22.1046 21 23 20.1046 23 19V5C23 3.89543 22.1046 3 21 3Z">
                                        </path>
                                        <path d="M10 4V20"></path>
                                        <path d="M4 7H7"></path>
                                        <path d="M4 10H7"></path>
                                        <path d="M4 13H7"></path>
                                    </svg></div>
                            </div>
                        </div>
                        <div class="workspace-tab-container">
                            <div class="workspace-leaf">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content" data-type="file-explorer">
                                    <div class="nav-header">
                                        <div class="nav-buttons-container">
                                            <div class="clickable-icon nav-action-button" aria-label="New note"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-edit">
                                                    <path
                                                        d="M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7">
                                                    </path>
                                                    <path
                                                        d="M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z">
                                                    </path>
                                                </svg></div>
                                            <div class="clickable-icon nav-action-button" aria-label="New folder"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-folder-plus">
                                                    <path d="M12 10v6"></path>
                                                    <path d="M9 13h6"></path>
                                                    <path
                                                        d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z">
                                                    </path>
                                                </svg></div>
                                            <div class="clickable-icon nav-action-button"
                                                aria-label="Change sort order"><svg xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                    stroke-linejoin="round" class="svg-icon lucide-sort-asc">
                                                    <path d="m3 8 4-4 4 4"></path>
                                                    <path d="M7 4v16"></path>
                                                    <path d="M11 12h4"></path>
                                                    <path d="M11 16h7"></path>
                                                    <path d="M11 20h10"></path>
                                                </svg></div>
                                            <div class="clickable-icon nav-action-button"
                                                aria-label="Auto-reveal current file"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-gallery-vertical">
                                                    <path d="M3 2h18"></path>
                                                    <rect x="3" y="6" width="18" height="12" rx="2"></rect>
                                                    <path d="M3 22h18"></path>
                                                </svg></div>
                                            <div class="clickable-icon nav-action-button" aria-label="Collapse all"><svg
                                                    xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                    viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-chevrons-down-up">
                                                    <path d="m7 20 5-5 5 5"></path>
                                                    <path d="m7 4 5 5 5-5"></path>
                                                </svg></div>
                                        </div>
                                    </div>
                                    <div class="nav-files-container node-insert-event" style="position: relative;">
                                        <div style="">
                                            <div style="width: 327px; height: 0.1px; margin-bottom: 0px;"></div>
                                            <div class="tree-item nav-folder is-collapsed">
                                                <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                    data-path="Login Details" draggable="true"
                                                    style="margin-inline-start: 0px !important; padding-inline-start: 24px !important;">
                                                    <div class="tree-item-icon collapse-icon is-collapsed"><svg
                                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="svg-icon right-triangle">
                                                            <path d="M3 8L12 17L21 8"></path>
                                                        </svg></div>
                                                    <div class="tree-item-inner nav-folder-title-content">Login Details
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tree-item nav-folder is-collapsed">
                                                <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                    data-path="Personal" draggable="true"
                                                    style="margin-inline-start: 0px !important; padding-inline-start: 24px !important;">
                                                    <div class="tree-item-icon collapse-icon is-collapsed"><svg
                                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="svg-icon right-triangle">
                                                            <path d="M3 8L12 17L21 8"></path>
                                                        </svg></div>
                                                    <div class="tree-item-inner nav-folder-title-content">Personal</div>
                                                </div>
                                            </div>
                                            <div class="tree-item nav-folder">
                                                <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                    data-path="Requirements" draggable="true"
                                                    style="margin-inline-start: 0px !important; padding-inline-start: 24px !important;">
                                                    <div class="tree-item-icon collapse-icon"><svg
                                                            xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                            viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                            stroke-width="2" stroke-linecap="round"
                                                            stroke-linejoin="round" class="svg-icon right-triangle">
                                                            <path d="M3 8L12 17L21 8"></path>
                                                        </svg></div>
                                                    <div class="tree-item-inner nav-folder-title-content">Requirements
                                                    </div>
                                                </div>
                                                <div class="tree-item-children nav-folder-children" style="">
                                                    <div style="width: 310px; height: 0.1px; margin-bottom: 0px;"></div>
                                                    <div class="tree-item nav-folder is-collapsed">
                                                        <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                            data-path="Requirements/CanHav - Hiral Panchal"
                                                            draggable="true"
                                                            style="margin-inline-start: -17px !important; padding-inline-start: 41px !important;">
                                                            <div class="tree-item-icon collapse-icon is-collapsed"><svg
                                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="svg-icon right-triangle">
                                                                    <path d="M3 8L12 17L21 8"></path>
                                                                </svg></div>
                                                            <div class="tree-item-inner nav-folder-title-content">CanHav
                                                                - Hiral Panchal</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-item nav-folder is-collapsed">
                                                        <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                            data-path="Requirements/Dr.Batra" draggable="true"
                                                            style="margin-inline-start: -17px !important; padding-inline-start: 41px !important;">
                                                            <div class="tree-item-icon collapse-icon is-collapsed"><svg
                                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="svg-icon right-triangle">
                                                                    <path d="M3 8L12 17L21 8"></path>
                                                                </svg></div>
                                                            <div class="tree-item-inner nav-folder-title-content">
                                                                Dr.Batra</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-item nav-folder is-collapsed">
                                                        <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                            data-path="Requirements/MTS Requirement - Sonal"
                                                            draggable="true"
                                                            style="margin-inline-start: -17px !important; padding-inline-start: 41px !important;">
                                                            <div class="tree-item-icon collapse-icon is-collapsed"><svg
                                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="svg-icon right-triangle">
                                                                    <path d="M3 8L12 17L21 8"></path>
                                                                </svg></div>
                                                            <div class="tree-item-inner nav-folder-title-content">MTS
                                                                Requirement - Sonal</div>
                                                        </div>
                                                    </div>
                                                    <div class="tree-item nav-folder">
                                                        <div class="tree-item-self nav-folder-title is-clickable mod-collapsible"
                                                            data-path="Requirements/Senior living platform india - Bhoomika"
                                                            draggable="true"
                                                            style="margin-inline-start: -17px !important; padding-inline-start: 41px !important;">
                                                            <div class="tree-item-icon collapse-icon"><svg
                                                                    xmlns="http://www.w3.org/2000/svg" width="24"
                                                                    height="24" viewBox="0 0 24 24" fill="none"
                                                                    stroke="currentColor" stroke-width="2"
                                                                    stroke-linecap="round" stroke-linejoin="round"
                                                                    class="svg-icon right-triangle">
                                                                    <path d="M3 8L12 17L21 8"></path>
                                                                </svg></div>
                                                            <div class="tree-item-inner nav-folder-title-content">Senior
                                                                living platform india - Bhoomika</div>
                                                        </div>
                                                        <div class="tree-item-children nav-folder-children" style="">
                                                            <div
                                                                style="width: 293px; height: 0.1px; margin-bottom: 0px;">
                                                            </div>
                                                            <div class="tree-item nav-file">
                                                                <div class="tree-item-self nav-file-title tappable is-clickable is-active"
                                                                    data-path="Requirements/Senior living platform india - Bhoomika/Scope.md"
                                                                    draggable="true"
                                                                    style="margin-inline-start: -34px !important; padding-inline-start: 58px !important;">
                                                                    <div class="tree-item-inner nav-file-title-content">
                                                                        Scope</div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="tree-item nav-file">
                                                <div class="tree-item-self nav-file-title tappable is-clickable"
                                                    data-path="Ruffpad.md" draggable="true"
                                                    style="margin-inline-start: 0px !important; padding-inline-start: 24px !important;">
                                                    <div class="tree-item-inner nav-file-title-content">Ruffpad</div>
                                                </div>
                                            </div>
                                            <div class="tree-item nav-file">
                                                <div class="tree-item-self nav-file-title tappable is-clickable"
                                                    data-path="Today's Tasks.md" draggable="true"
                                                    style="margin-inline-start: 0px !important; padding-inline-start: 24px !important;">
                                                    <div class="tree-item-inner nav-file-title-content">Today's Tasks
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="workspace-split mod-vertical mod-root">
                    <hr class="workspace-leaf-resize-handle">
                    <div class="workspace-tabs mod-active mod-top">
                        <hr class="workspace-leaf-resize-handle">
                        <div class="workspace-tab-header-container">
                            <div class="workspace-tab-header-container-inner" style="--animation-dur: 250ms;">
                                <div class="workspace-tab-header tappable" draggable="true"
                                    aria-label="marketing_website" data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">marketing_website</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Server Details"
                                    data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Server Details</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="scope"
                                    data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">scope</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Scope"
                                    data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Scope</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Today's Tasks"
                                    data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Today's Tasks</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Today's Tasks"
                                    data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Today's Tasks</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable is-active mod-active" draggable="true"
                                    aria-label="Scope" data-tooltip-delay="300" data-type="markdown">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-file">
                                                <path d="M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z">
                                                </path>
                                                <path d="M14 2v4a2 2 0 0 0 2 2h4"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Scope</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                            </div>
                            <div class="workspace-tab-header-new-tab"><span class="clickable-icon"
                                    aria-label="New tab"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-plus">
                                        <path d="M5 12h14"></path>
                                        <path d="M12 5v14"></path>
                                    </svg></span></div>
                            <div class="workspace-tab-header-spacer"></div>
                            <div class="workspace-tab-header-tab-list"><span class="clickable-icon"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="svg-icon lucide-chevron-down">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></span></div>
                        </div>
                        <div class="workspace-tab-container">
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf mod-active">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content" data-type="markdown" data-mode="source">
                                    <div class="view-header">
                                        <div class="view-header-left">
                                            <div class="view-header-nav-buttons"><button class="clickable-icon"
                                                    aria-disabled="true" aria-label="Navigate back"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-arrow-left">
                                                        <path d="m12 19-7-7 7-7"></path>
                                                        <path d="M19 12H5"></path>
                                                    </svg></button><button class="clickable-icon" aria-disabled="true"
                                                    aria-label="Navigate forward"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-arrow-right">
                                                        <path d="M5 12h14"></path>
                                                        <path d="m12 5 7 7-7 7"></path>
                                                    </svg></button></div>
                                        </div>
                                        <div class="view-header-title-container mod-at-start mod-fade">
                                            <div class="view-header-title-parent"><span
                                                    class="view-header-breadcrumb">Requirements</span><span
                                                    class="view-header-breadcrumb-separator">/</span><span
                                                    class="view-header-breadcrumb">Senior living platform india -
                                                    Bhoomika</span><span
                                                    class="view-header-breadcrumb-separator">/</span></div>
                                            <div class="view-header-title" tabindex="-1" contenteditable="true">Scope
                                            </div>
                                        </div>
                                        <div class="view-actions"><button
                                                class="clickable-icon view-action mod-bookmark"
                                                aria-label="Bookmark"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-bookmark">
                                                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z">
                                                    </path>
                                                </svg></button><button class="clickable-icon view-action" aria-label="Current view: editing
Click to read
⌘+Click to open to the right"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                                    fill="none" stroke="currentColor" stroke-width="2"
                                                    stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-book-open">
                                                    <path d="M12 7v14"></path>
                                                    <path
                                                        d="M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z">
                                                    </path>
                                                </svg></button><button class="clickable-icon view-action"
                                                aria-label="More options"><svg xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                    stroke-linejoin="round" class="svg-icon lucide-more-vertical">
                                                    <circle cx="12" cy="12" r="1"></circle>
                                                    <circle cx="12" cy="5" r="1"></circle>
                                                    <circle cx="12" cy="19" r="1"></circle>
                                                </svg></button></div>
                                    </div>
                                    <div class="view-content">
                                        <div class="markdown-source-view cm-s-obsidian mod-cm6 node-insert-event is-readable-line-width is-live-preview is-folding show-properties"
                                            style="">
                                            <div class="cm-editor cm-focused ͼ1 ͼ2 ">
                                                <div class="cm-announced" aria-live="polite"></div>
                                                <div tabindex="-1" class="cm-scroller">
                                                    <div class="cm-sizer">
                                                        <div class="inline-title" contenteditable="true"
                                                            spellcheck="true" autocapitalize="on" tabindex="-1"
                                                            enterkeyhint="done">Scope</div>
                                                        <div class="metadata-container" tabindex="-1"
                                                            data-property-count="0">
                                                            <div class="metadata-error-container"
                                                                style="display: none;"></div>
                                                            <div class="metadata-properties-heading" tabindex="0">
                                                                <div class="collapse-indicator collapse-icon"><svg
                                                                        xmlns="http://www.w3.org/2000/svg" width="24"
                                                                        height="24" viewBox="0 0 24 24" fill="none"
                                                                        stroke="currentColor" stroke-width="2"
                                                                        stroke-linecap="round" stroke-linejoin="round"
                                                                        class="svg-icon right-triangle">
                                                                        <path d="M3 8L12 17L21 8"></path>
                                                                    </svg></div>
                                                                <div class="metadata-properties-title">Properties</div>
                                                            </div>
                                                            <div class="metadata-content">
                                                                <div class="metadata-properties"></div>
                                                                <div class="metadata-add-button text-icon-button"
                                                                    tabindex="0"><span class="text-button-icon"><svg
                                                                            xmlns="http://www.w3.org/2000/svg"
                                                                            width="24" height="24" viewBox="0 0 24 24"
                                                                            fill="none" stroke="currentColor"
                                                                            stroke-width="2" stroke-linecap="round"
                                                                            stroke-linejoin="round"
                                                                            class="svg-icon lucide-plus">
                                                                            <path d="M5 12h14"></path>
                                                                            <path d="M12 5v14"></path>
                                                                        </svg></span><span class="text-button-label">Add
                                                                        property</span></div>
                                                            </div>
                                                        </div>
                                                        <div class="cm-contentContainer">
                                                            <div spellcheck="true" autocorrect="on" autocapitalize="on"
                                                                writingsuggestions="false" translate="no"
                                                                contenteditable="true"
                                                                style="tab-size: 4; padding-bottom: 488px;"
                                                                class="cm-content cm-lineWrapping" role="textbox"
                                                                aria-multiline="true" data-language="hypermd">
                                                                <div class="HyperMD-header HyperMD-header-1 cm-line"
                                                                    dir="ltr">
                                                                    <div class="cm-fold-indicator"
                                                                        contenteditable="false">&ZeroWidthSpace;<div
                                                                            class="collapse-indicator collapse-icon">
                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon right-triangle">
                                                                                <path d="M3 8L12 17L21 8"></path>
                                                                            </svg></div>
                                                                    </div><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><span
                                                                        contenteditable="false"></span><img
                                                                        class="cm-widgetBuffer" aria-hidden="true"><span
                                                                        class="cm-header cm-header-1">Senior Living
                                                                        Platform India</span>
                                                                </div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="cm-active HyperMD-header HyperMD-header-2 cm-line"
                                                                    dir="ltr">
                                                                    <div class="cm-fold-indicator"
                                                                        contenteditable="false">&ZeroWidthSpace;<div
                                                                            class="collapse-indicator collapse-icon">
                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon right-triangle">
                                                                                <path d="M3 8L12 17L21 8"></path>
                                                                            </svg></div>
                                                                    </div><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><span
                                                                        class="cm-formatting cm-formatting-header cm-formatting-header-2 cm-header cm-header-2">##
                                                                    </span><span class="cm-header cm-header-2">Project
                                                                        Overview</span>
                                                                </div>
                                                                <div class="cm-line" dir="ltr">Senior Living is a
                                                                    first-of-its-kind integrated digital platform
                                                                    focused exclusively on senior living in India. The
                                                                    platform aims to be an informative and trustworthy
                                                                    space where individuals and families can explore and
                                                                    evaluate senior living options across the country.
                                                                    Unlike a brokerage site, Senior Living will provide
                                                                    verified information, transparent evaluations, and
                                                                    real resident insights to help users make informed
                                                                    decisions about senior living arrangements.</div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="HyperMD-header HyperMD-header-2 cm-line"
                                                                    dir="ltr">
                                                                    <div class="cm-fold-indicator"
                                                                        contenteditable="false">&ZeroWidthSpace;<div
                                                                            class="collapse-indicator collapse-icon">
                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon right-triangle">
                                                                                <path d="M3 8L12 17L21 8"></path>
                                                                            </svg></div>
                                                                    </div><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><span
                                                                        contenteditable="false"></span><img
                                                                        class="cm-widgetBuffer" aria-hidden="true"><span
                                                                        class="cm-header cm-header-2">Target
                                                                        Audience</span>
                                                                </div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Senior citizens looking for
                                                                        retirement homes or assisted living
                                                                        options</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Adult children researching
                                                                        living options for their aging parents</span>
                                                                </div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Caregivers and family members
                                                                        supporting seniors in transition</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Senior living community
                                                                        operators and service providers</span></div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="HyperMD-header HyperMD-header-2 cm-line"
                                                                    dir="ltr">
                                                                    <div class="cm-fold-indicator"
                                                                        contenteditable="false">&ZeroWidthSpace;<div
                                                                            class="collapse-indicator collapse-icon">
                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon right-triangle">
                                                                                <path d="M3 8L12 17L21 8"></path>
                                                                            </svg></div>
                                                                    </div><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><span
                                                                        contenteditable="false"></span><img
                                                                        class="cm-widgetBuffer" aria-hidden="true"><span
                                                                        class="cm-header cm-header-2">User Role
                                                                        Approach</span>
                                                                </div>
                                                                <div class="cm-line" dir="ltr">All registered users will
                                                                    have access to the core platform functionality.
                                                                    Users can:</div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Search and browse
                                                                        properties</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Participate in community
                                                                        discussions</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Make inquiries about
                                                                        properties</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Access the marketplace as
                                                                        buyers</span></div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="cm-line" dir="ltr">Additional
                                                                    roles/permissions are activated through specific
                                                                    onboarding flows:</div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Property owners/managers
                                                                        (after verification)</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Product vendors (after
                                                                        marketplace seller onboarding)</span></div>
                                                                <div class="HyperMD-list-line HyperMD-list-line-1 cm-line"
                                                                    dir="ltr"
                                                                    style="text-indent: -23px; padding-inline-start: 23px;">
                                                                    <span
                                                                        class="cm-formatting cm-formatting-list cm-formatting-list-ul cm-list-1"><span
                                                                            class="list-bullet">-</span> </span><span
                                                                        class="cm-list-1">Content contributors (after
                                                                        approval process)</span></div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="cm-line" dir="ltr">Payment functionality is
                                                                    primarily required for the marketplace module and
                                                                    will be integrated into the relevant onboarding
                                                                    processes.</div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="HyperMD-header HyperMD-header-2 cm-line"
                                                                    dir="ltr">
                                                                    <div class="cm-fold-indicator"
                                                                        contenteditable="false">&ZeroWidthSpace;<div
                                                                            class="collapse-indicator collapse-icon">
                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon right-triangle">
                                                                                <path d="M3 8L12 17L21 8"></path>
                                                                            </svg></div>
                                                                    </div><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><img class="cm-widgetBuffer"
                                                                        aria-hidden="true"><span
                                                                        contenteditable="false"></span><img
                                                                        class="cm-widgetBuffer" aria-hidden="true"><span
                                                                        class="cm-header cm-header-2">Website Features
                                                                        (MVP)</span>
                                                                </div>
                                                                <div class="cm-line" dir="ltr"><br></div>
                                                                <div class="cm-embed-block cm-table-widget markdown-rendered"
                                                                    contenteditable="false" dir="auto">
                                                                    <div class="table-wrapper">
                                                                        <table class="table-editor" tabindex="-1">
                                                                            <thead>
                                                                                <tr>
                                                                                    <th dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            #</div>
                                                                                        <div class="table-col-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-horizontal">
                                                                                                <circle cx="12" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="12" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="15"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </th>
                                                                                    <th dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Module</div>
                                                                                        <div class="table-col-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-horizontal">
                                                                                                <circle cx="12" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="12" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="15"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </th>
                                                                                    <th dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Section</div>
                                                                                        <div class="table-col-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-horizontal">
                                                                                                <circle cx="12" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="12" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="15"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </th>
                                                                                    <th dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Tasks/Features</div>
                                                                                        <div class="table-col-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-horizontal">
                                                                                                <circle cx="12" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="9"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="12" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="19" cy="15"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="5" cy="15"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </th>
                                                                                </tr>
                                                                            </thead>
                                                                            <tbody>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            1</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>User
                                                                                                Management</strong>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Registration &amp;
                                                                                            Authentication</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can create accounts
                                                                                            using email credentials,
                                                                                            with basic verification
                                                                                            processes to confirm
                                                                                            identity. The system
                                                                                            provides password recovery
                                                                                            workflows and session
                                                                                            management for secure
                                                                                            access.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Profile Management</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users maintain basic
                                                                                            profiles containing personal
                                                                                            information and preferences.
                                                                                            The system includes simple
                                                                                            privacy controls allowing
                                                                                            users to determine what
                                                                                            information is visible to
                                                                                            others.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            2</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Property
                                                                                                Management</strong>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Listings</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform presents senior
                                                                                            living properties in an
                                                                                            organized, searchable format
                                                                                            with filtering options based
                                                                                            on location, amenities, and
                                                                                            price range. Users can save
                                                                                            favorite properties for
                                                                                            future reference.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Details</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Each property listing
                                                                                            provides essential
                                                                                            information including
                                                                                            photos, floor plans, and
                                                                                            pricing. The system
                                                                                            highlights key amenities,
                                                                                            services, and accessibility
                                                                                            features to help users make
                                                                                            informed decisions.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Comparison</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can select up to three
                                                                                            properties for side-by-side
                                                                                            comparison across key
                                                                                            criteria including features,
                                                                                            costs, and amenities. The
                                                                                            comparison tool highlights
                                                                                            differences to aid in
                                                                                            decision-making.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Submission</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property owners and managers
                                                                                            can submit new listings
                                                                                            through a structured form
                                                                                            that collects essential
                                                                                            information and media
                                                                                            assets. The system includes
                                                                                            basic verification workflows
                                                                                            to ensure accuracy.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            3</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Booking
                                                                                                System</strong></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Inquiry Management</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can submit inquiries
                                                                                            about properties through
                                                                                            contact forms, with the
                                                                                            system tracking
                                                                                            communications. Property
                                                                                            managers receive
                                                                                            notifications about new
                                                                                            inquiries and can respond
                                                                                            through the platform.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Visit Scheduling</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform facilitates
                                                                                            property tour scheduling by
                                                                                            displaying availability
                                                                                            calendars and allowing users
                                                                                            to book visits. The system
                                                                                            sends confirmation and
                                                                                            reminder notifications to
                                                                                            both parties.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            4</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Marketplace</strong>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Product Catalog</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The marketplace showcases
                                                                                            essential senior-focused
                                                                                            products organized by
                                                                                            categories. Users can browse
                                                                                            and search products with
                                                                                            basic filters for specific
                                                                                            needs.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Product Details</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Each product listing
                                                                                            includes basic information,
                                                                                            images, pricing details, and
                                                                                            availability status. The
                                                                                            system highlights key
                                                                                            features relevant to senior
                                                                                            users.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Shopping Cart</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can add products to
                                                                                            their cart, adjust
                                                                                            quantities, and complete
                                                                                            purchases. The system
                                                                                            calculates taxes and
                                                                                            shipping costs and provides
                                                                                            order summaries.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Checkout Process</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform offers a
                                                                                            senior-friendly checkout
                                                                                            experience with simplified
                                                                                            steps and standard payment
                                                                                            methods. Users can manage
                                                                                            shipping addresses and
                                                                                            review orders before
                                                                                            completion.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            5</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Community
                                                                                                Platform</strong></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Blog System</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform publishes
                                                                                            informative articles about
                                                                                            senior living topics,
                                                                                            organized by categories for
                                                                                            easy navigation. The system
                                                                                            supports basic commenting
                                                                                            and social sharing
                                                                                            functionality.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Resource Library</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can access essential
                                                                                            guides and educational
                                                                                            materials related to senior
                                                                                            living decisions. The system
                                                                                            organizes resources by
                                                                                            categories and enables basic
                                                                                            searching.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            6</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Review
                                                                                                System</strong></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Reviews</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Verified users can submit
                                                                                            reviews of senior living
                                                                                            properties, including
                                                                                            ratings and text feedback.
                                                                                            The system displays
                                                                                            verification badges and
                                                                                            allows management responses
                                                                                            to help prospective
                                                                                            residents make informed
                                                                                            decisions.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            7</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Search
                                                                                                System</strong></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Property Search</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform offers
                                                                                            specialized property search
                                                                                            functionality with
                                                                                            parameters including
                                                                                            location, amenities, and
                                                                                            price range. Users can view
                                                                                            properties on a map and sort
                                                                                            results by relevance,
                                                                                            distance, or price.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Saved Searches</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can save search
                                                                                            criteria for properties
                                                                                            they're interested in and
                                                                                            receive email alerts when
                                                                                            new matches become
                                                                                            available.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            8</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Content
                                                                                                Management</strong>
                                                                                        </div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Static Pages</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform includes
                                                                                            essential informational
                                                                                            pages about the service,
                                                                                            contact details, and
                                                                                            frequently asked questions.
                                                                                            Content is maintained to
                                                                                            ensure accurate information
                                                                                            for users.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            9</div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <strong>Accessibility
                                                                                                Features</strong></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            UI Enhancements</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            The platform incorporates
                                                                                            basic senior-friendly design
                                                                                            elements including
                                                                                            adjustable text size and
                                                                                            screen reader compatibility.
                                                                                            The system ensures keyboard
                                                                                            navigation for users with
                                                                                            motor limitations.</div>
                                                                                    </td>
                                                                                </tr>
                                                                                <tr>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                        <div class="table-row-drag-handle"
                                                                                            data-ignore-swipe="true">
                                                                                            <svg xmlns="http://www.w3.org/2000/svg"
                                                                                                width="24" height="24"
                                                                                                viewBox="0 0 24 24"
                                                                                                fill="none"
                                                                                                stroke="currentColor"
                                                                                                stroke-width="2"
                                                                                                stroke-linecap="round"
                                                                                                stroke-linejoin="round"
                                                                                                class="svg-icon lucide-grip-vertical">
                                                                                                <circle cx="9" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="9" cy="19"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="12"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="5"
                                                                                                    r="1"></circle>
                                                                                                <circle cx="15" cy="19"
                                                                                                    r="1"></circle>
                                                                                            </svg></div>
                                                                                    </td>
                                                                                    <td dir="auto">
                                                                                        <div class="table-cell-wrapper">
                                                                                            <br></div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Help System</div>
                                                                                    </td>
                                                                                    <td dir="ltr">
                                                                                        <div class="table-cell-wrapper">
                                                                                            Users can access basic help
                                                                                            resources including tooltips
                                                                                            and a searchable help center
                                                                                            with guides on using the
                                                                                            platform's core features.
                                                                                        </div>
                                                                                    </td>
                                                                                </tr>
                                                                            </tbody>
                                                                        </table>
                                                                        <div class="table-row-btn"
                                                                            aria-label="Add row after"><svg
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon lucide-plus">
                                                                                <path d="M5 12h14"></path>
                                                                                <path d="M12 5v14"></path>
                                                                            </svg></div>
                                                                        <div class="table-col-btn"
                                                                            aria-label="Add column after"><svg
                                                                                xmlns="http://www.w3.org/2000/svg"
                                                                                width="24" height="24"
                                                                                viewBox="0 0 24 24" fill="none"
                                                                                stroke="currentColor" stroke-width="2"
                                                                                stroke-linecap="round"
                                                                                stroke-linejoin="round"
                                                                                class="svg-icon lucide-plus">
                                                                                <path d="M5 12h14"></path>
                                                                                <path d="M12 5v14"></path>
                                                                            </svg></div>
                                                                    </div>
                                                                </div>
                                                                <div class="cm-gap" style="height: 386px;"></div>
                                                            </div>
                                                        </div>
                                                        <div class="embedded-backlinks" style="display: none;"></div>
                                                    </div>
                                                    <div class="cm-layer cm-layer-above cm-cursorLayer"
                                                        aria-hidden="true"
                                                        style="z-index: 150; animation-duration: 1200ms;">
                                                        <div class="cm-cursor cm-cursor-primary"
                                                            style="left: 32px; top: 155.594px; height: 30.75px;"></div>
                                                    </div>
                                                    <div class="cm-layer cm-selectionLayer" aria-hidden="true"
                                                        style="z-index: -2;"></div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="markdown-reading-view"
                                            style="width: 100%; height: 100%; display: none;">
                                            <div
                                                class="markdown-preview-view markdown-rendered node-insert-event is-readable-line-width allow-fold-headings allow-fold-lists show-indentation-guide show-properties">
                                                <div class="markdown-preview-sizer markdown-preview-section"
                                                    style="padding-bottom: 0px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="workspace-split mod-horizontal mod-sidedock mod-right-split" style="width: 300px;">
                    <hr class="workspace-leaf-resize-handle">
                    <div class="workspace-sidedock-empty-state" style="display: none;">
                        <p class="u-muted">The sidebar is empty, try dragging a tab here.</p>
                    </div>
                    <div class="workspace-tabs mod-top mod-top-right-space">
                        <hr class="workspace-leaf-resize-handle">
                        <div class="workspace-tab-header-container">
                            <div class="workspace-tab-header-container-inner" style="--animation-dur: 250ms;">
                                <div class="workspace-tab-header tappable is-active" draggable="true"
                                    aria-label="Backlinks for Scope" data-tooltip-delay="300" data-type="backlink">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon links-coming-in">
                                                <path
                                                    d="M8.70467 12C8.21657 11.6404 7.81269 11.1817 7.52044 10.6549C7.22819 10.1281 7.0544 9.54553 7.01086 8.94677C6.96732 8.348 7.05504 7.74701 7.26808 7.18456C7.48112 6.62212 7.81449 6.11138 8.24558 5.68697L10.7961 3.17516C11.5978 2.41258 12.6716 1.99062 13.7861 2.00016C14.9007 2.0097 15.9668 2.44997 16.755 3.22615C17.5431 4.00234 17.9902 5.05233 17.9998 6.14998C18.0095 7.24763 17.5811 8.30511 16.8067 9.09467L15.9014 10">
                                                </path>
                                                <path
                                                    d="M11.2953 8C11.7834 8.35957 12.1873 8.81831 12.4796 9.34512C12.7718 9.87192 12.9456 10.4545 12.9891 11.0532C13.0327 11.652 12.945 12.253 12.7319 12.8154C12.5189 13.3779 12.1855 13.8886 11.7544 14.313L9.20392 16.8248C8.40221 17.5874 7.32844 18.0094 6.21389 17.9998C5.09933 17.9903 4.03318 17.55 3.24504 16.7738C2.4569 15.9977 2.00985 14.9477 2.00016 13.85C1.99047 12.7524 2.41893 11.6949 3.19326 10.9053L4.09859 10">
                                                </path>
                                                <path d="M17 21L14 18L17 15"></path>
                                                <path d="M21 18H14"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Backlinks for Scope</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true"
                                    aria-label="Outgoing links from Karan Files" data-tooltip-delay="300"
                                    data-type="outgoing-link">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon links-going-out">
                                                <path
                                                    d="M8.70467 12C8.21657 11.6404 7.81269 11.1817 7.52044 10.6549C7.22819 10.1281 7.0544 9.54553 7.01086 8.94677C6.96732 8.348 7.05504 7.74701 7.26808 7.18456C7.48112 6.62212 7.81449 6.11138 8.24558 5.68697L10.7961 3.17516C11.5978 2.41258 12.6716 1.99062 13.7861 2.00016C14.9007 2.0097 15.9668 2.44997 16.755 3.22615C17.5431 4.00234 17.9902 5.05233 17.9998 6.14998C18.0095 7.24763 17.5811 8.30511 16.8067 9.09467L15.9014 10">
                                                </path>
                                                <path
                                                    d="M11.2953 8C11.7834 8.35957 12.1873 8.81831 12.4796 9.34512C12.7718 9.87192 12.9456 10.4545 12.9891 11.0532C13.0327 11.652 12.945 12.253 12.7319 12.8154C12.5189 13.3779 12.1855 13.8886 11.7544 14.313L9.20392 16.8248C8.40221 17.5874 7.32844 18.0094 6.21389 17.9998C5.09933 17.9903 4.03318 17.55 3.24504 16.7738C2.4569 15.9977 2.00985 14.9477 2.00016 13.85C1.99047 12.7524 2.41893 11.6949 3.19326 10.9053L4.09859 10">
                                                </path>
                                                <path d="M18 21L21 18L18 15"></path>
                                                <path d="M14 18H21"></path>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Outgoing links from Karan Files
                                        </div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true" aria-label="Tags"
                                    data-tooltip-delay="300" data-type="tag">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-tags">
                                                <path d="m15 5 6.3 6.3a2.4 2.4 0 0 1 0 3.4L17 19"></path>
                                                <path
                                                    d="M9.586 5.586A2 2 0 0 0 8.172 5H3a1 1 0 0 0-1 1v5.172a2 2 0 0 0 .586 1.414L8.29 18.29a2.426 2.426 0 0 0 3.42 0l3.58-3.58a2.426 2.426 0 0 0 0-3.42z">
                                                </path>
                                                <circle cx="6.5" cy="9.5" r="0.5"></circle>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Tags</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                                <div class="workspace-tab-header tappable" draggable="true"
                                    aria-label="Outline of Karan Files" data-tooltip-delay="300" data-type="outline">
                                    <div class="workspace-tab-header-inner">
                                        <div class="workspace-tab-header-inner-icon"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-list">
                                                <line x1="8" y1="6" x2="21" y2="6"></line>
                                                <line x1="8" y1="12" x2="21" y2="12"></line>
                                                <line x1="8" y1="18" x2="21" y2="18"></line>
                                                <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                                <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                                <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                            </svg></div>
                                        <div class="workspace-tab-header-inner-title">Outline of Karan Files</div>
                                        <div class="workspace-tab-header-status-container"></div>
                                        <div class="workspace-tab-header-inner-close-button" aria-label="Close"><svg
                                                xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                                stroke-linecap="round" stroke-linejoin="round"
                                                class="svg-icon lucide-x">
                                                <path d="M18 6 6 18"></path>
                                                <path d="m6 6 12 12"></path>
                                            </svg></div>
                                    </div>
                                </div>
                            </div>
                            <div class="workspace-tab-header-new-tab"><span class="clickable-icon"
                                    aria-label="New tab"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                        viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                                        stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-plus">
                                        <path d="M5 12h14"></path>
                                        <path d="M12 5v14"></path>
                                    </svg></span></div>
                            <div class="workspace-tab-header-spacer"></div>
                            <div class="workspace-tab-header-tab-list"><span class="clickable-icon"><svg
                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                                        fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                        stroke-linejoin="round" class="svg-icon lucide-chevron-down">
                                        <path d="m6 9 6 6 6-6"></path>
                                    </svg></span></div>
                            <div class="sidebar-toggle-button mod-right" aria-label="" data-tooltip-position="left">
                                <div class="clickable-icon"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                        height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                        class="svg-icon sidebar-right">
                                        <path
                                            d="M3 3H21C22.1046 3 23 3.89543 23 5V19C23 20.1046 22.1046 21 21 21H3C1.89543 21 1 20.1046 1 19V5C1 3.89543 1.89543 3 3 3Z">
                                        </path>
                                        <path d="M14 4V20"></path>
                                        <path d="M20 7H17"></path>
                                        <path d="M20 10H17"></path>
                                        <path d="M20 13H17"></path>
                                    </svg></div>
                            </div>
                        </div>
                        <div class="workspace-tab-container">
                            <div class="workspace-leaf">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content" data-type="backlink">
                                    <div class="view-header">
                                        <div class="view-header-left">
                                            <div class="view-header-nav-buttons"><button class="clickable-icon"
                                                    aria-disabled="true" aria-label="Navigate back"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-arrow-left">
                                                        <path d="m12 19-7-7 7-7"></path>
                                                        <path d="M19 12H5"></path>
                                                    </svg></button><button class="clickable-icon" aria-disabled="true"
                                                    aria-label="Navigate forward"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-arrow-right">
                                                        <path d="M5 12h14"></path>
                                                        <path d="m12 5 7 7-7 7"></path>
                                                    </svg></button></div>
                                        </div>
                                        <div class="view-header-title-container mod-at-start mod-fade">
                                            <div class="view-header-title-parent"><span
                                                    class="view-header-breadcrumb">Requirements</span><span
                                                    class="view-header-breadcrumb-separator">/</span><span
                                                    class="view-header-breadcrumb">Senior living platform india -
                                                    Bhoomika</span><span
                                                    class="view-header-breadcrumb-separator">/</span></div>
                                            <div class="view-header-title">Backlinks for Scope</div>
                                        </div>
                                        <div class="view-actions"><button
                                                class="clickable-icon view-action mod-bookmark"
                                                aria-label="Bookmark"><svg xmlns="http://www.w3.org/2000/svg" width="24"
                                                    height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                    stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                    class="svg-icon lucide-bookmark">
                                                    <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z">
                                                    </path>
                                                </svg></button><button class="clickable-icon view-action"
                                                aria-label="More options"><svg xmlns="http://www.w3.org/2000/svg"
                                                    width="24" height="24" viewBox="0 0 24 24" fill="none"
                                                    stroke="currentColor" stroke-width="2" stroke-linecap="round"
                                                    stroke-linejoin="round" class="svg-icon lucide-more-vertical">
                                                    <circle cx="12" cy="12" r="1"></circle>
                                                    <circle cx="12" cy="5" r="1"></circle>
                                                    <circle cx="12" cy="19" r="1"></circle>
                                                </svg></button></div>
                                    </div>
                                    <div class="view-content">
                                        <div class="nav-header">
                                            <div class="nav-buttons-container">
                                                <div class="clickable-icon nav-action-button"
                                                    aria-label="Collapse results"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-list">
                                                        <line x1="8" y1="6" x2="21" y2="6"></line>
                                                        <line x1="8" y1="12" x2="21" y2="12"></line>
                                                        <line x1="8" y1="18" x2="21" y2="18"></line>
                                                        <line x1="3" y1="6" x2="3.01" y2="6"></line>
                                                        <line x1="3" y1="12" x2="3.01" y2="12"></line>
                                                        <line x1="3" y1="18" x2="3.01" y2="18"></line>
                                                    </svg></div>
                                                <div class="clickable-icon nav-action-button"
                                                    aria-label="Show more context"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-move-vertical">
                                                        <polyline points="8 18 12 22 16 18"></polyline>
                                                        <polyline points="8 6 12 2 16 6"></polyline>
                                                        <line x1="12" y1="2" x2="12" y2="22"></line>
                                                    </svg></div>
                                                <div class="clickable-icon nav-action-button"
                                                    aria-label="Change sort order"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-sort-asc">
                                                        <path d="m3 8 4-4 4 4"></path>
                                                        <path d="M7 4v16"></path>
                                                        <path d="M11 12h4"></path>
                                                        <path d="M11 16h7"></path>
                                                        <path d="M11 20h10"></path>
                                                    </svg></div>
                                                <div class="clickable-icon nav-action-button"
                                                    aria-label="Show search filter"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon lucide-search">
                                                        <circle cx="11" cy="11" r="8"></circle>
                                                        <path d="m21 21-4.3-4.3"></path>
                                                    </svg></div>
                                            </div>
                                            <div class="search-input-container" style="display: none;"><input
                                                    enterkeyhint="search" type="search" spellcheck="false"
                                                    placeholder="Search...">
                                                <div class="search-input-clear-button" aria-label="Clear search"></div>
                                            </div>
                                        </div>
                                        <div class="backlink-pane node-insert-event" style="position: relative;">
                                            <div class="tree-item-self is-clickable" aria-label="Click to collapse"
                                                data-tooltip-position="left"><span
                                                    class="tree-item-icon collapse-icon"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon right-triangle">
                                                        <path d="M3 8L12 17L21 8"></path>
                                                    </svg></span>
                                                <div class="tree-item-inner">Linked mentions</div>
                                                <div class="tree-item-flair-outer"><span
                                                        class="tree-item-flair">0</span></div>
                                            </div>
                                            <div class="search-result-container">
                                                <div class="search-results-children" style="min-height: 0px;"></div>
                                                <div class="search-empty-state">No backlinks found.</div>
                                            </div>
                                            <div class="tree-item-self is-clickable is-collapsed"
                                                aria-label="Click to expand" data-tooltip-position="left"><span
                                                    class="tree-item-icon collapse-icon is-collapsed"><svg
                                                        xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                                                        viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                                        stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                                        class="svg-icon right-triangle">
                                                        <path d="M3 8L12 17L21 8"></path>
                                                    </svg></span>
                                                <div class="tree-item-inner">Unlinked mentions</div>
                                                <div class="tree-item-flair-outer"><span class="tree-item-flair"
                                                        style="display: none;"></span></div>
                                            </div>
                                            <div class="search-result-container" style="display: none;">
                                                <div class="search-results-children" style="min-height: 0px;"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                            <div class="workspace-leaf" style="display: none;">
                                <hr class="workspace-leaf-resize-handle">
                                <div class="workspace-leaf-content node-insert-event" data-type="undefined"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="workspace-ribbon side-dock-ribbon mod-right"></div>
            </div>
        </div>
        <div class="status-bar">
            <div class="status-bar-item plugin-backlink mod-clickable" style="">0 backlinks</div>
            <div class="status-bar-item plugin-editor-status mod-clickable" style="" aria-label="Live Preview"
                data-tooltip-position="top"><span class="status-bar-item-icon"><svg xmlns="http://www.w3.org/2000/svg"
                        width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                        stroke-linecap="round" stroke-linejoin="round" class="svg-icon lucide-edit-3">
                        <path d="M12 20h9"></path>
                        <path
                            d="M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z">
                        </path>
                    </svg></span></div>
            <div class="status-bar-item plugin-word-count" style=""><span class="status-bar-item-segment">0
                    words</span><span class="status-bar-item-segment">1 character</span></div>
            <div class="status-bar-item plugin-sync" aria-label="Uninitialized" data-tooltip-position="top">
                <div class="status-bar-item-segment"><span class="status-bar-item-icon sync-status-icon mod-error"><svg
                            xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                            class="svg-icon refresh-cw-off">
                            <path d="M21 8L18.74 5.74A9.75 9.75 0 0 0 12 3C11 3 10.03 3.16 9.13 3.47"></path>
                            <path d="M8 16H3v5"></path>
                            <path d="M3 12C3 9.51 4 7.26 5.64 5.64"></path>
                            <path d="m3 16 2.26 2.26A9.75 9.75 0 0 0 12 21c2.49 0 4.74-1 6.36-2.64"></path>
                            <path d="M21 12c0 1-.16 1.97-.47 2.87"></path>
                            <path d="M21 3v5h-5"></path>
                            <path d="M22 22 2 2"></path>
                        </svg></span></div>
            </div>
        </div>
    </div>
</body>

</html>