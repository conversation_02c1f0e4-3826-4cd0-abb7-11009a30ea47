# KaptureHQ

> **Development Status**: ✅ Modern UI Complete - Professional Knowledge Management App

KaptureHQ is a beautiful desktop application with a Notion-inspired UI and powerful markdown editing capabilities. It combines a local-first vault system with flexible database features, built using React and Electron.js.

## Features

### 🗂️ Unified Page System
- ✅ Create and manage local vaults
- ✅ Unified page management (like Notion)
- ✅ Each page can be either markdown or database
- ✅ Simple flat file structure in vault root
- 🔄 Internal linking (wiki-style) between pages (Future)

### ✍️ Clean Markdown Editor (Working & Stable!)
- ✅ **Professional Markdown Editing** - Clean, reliable textarea-based editor
- ✅ **Obsidian-style Styling** - Beautiful typography and professional appearance
- ✅ **Fixed Title Layout** - Title left-aligned, save status right-aligned
- ✅ **Auto-save Functionality** - Automatic saving with visual feedback
- ✅ **Keyboard Shortcuts** - Ctrl+B for bold, Ctrl+I for italic, Ctrl+` for code
- ✅ **Direct Markdown Input** - No formatting toolbar needed, just type markdown
- ✅ **Fixed Title Handling** - Page title properly extracted from filename
- ✅ **Stable Operation** - No weird behavior, crashes, or infinite loops
- ✅ **Natural Text Input** - Proper backspace, line handling, and cursor behavior
- 🔄 **Hybrid Rendering** - Future enhancement (contentEditable has UX issues)

### 📊 Database Sheets
- ✅ CSV-based database storage
- ✅ Interactive table editing with add/remove rows and columns
- ✅ Basic cell editing and validation
- 🔄 Advanced column types (select, reference) (Future)
- 🔄 Reference relationships between sheets (Future)

### 💬 Collaboration Features (Future)
- 🔄 Comments system for database rows
- 🔄 Activity logging and change tracking
- 🔄 User attribution for changes

### 🎨 Modern UI (Notion-Inspired)
- ✅ Beautiful Notion-style interface with clean typography
- ✅ Professional sidebar with workspace branding
- ✅ Modern database tables with hover effects and smooth interactions
- ✅ Rich markdown editor with comprehensive formatting toolbar
- ✅ Dark/light theme support with refined color palette
- ✅ Responsive design with shadcn/ui components

## Tech Stack

- **Platform**: Desktop (Electron.js)
- **Frontend**: React + TypeScript
- **Editor**: CodeMirror 6 (Professional markdown editing)
- **Styling**: TailwindCSS + shadcn/ui + Radix UI
- **Data Storage**: Local filesystem (Markdown + CSV)
- **Build Tool**: Vite

## Development Setup

### Prerequisites
- Node.js (v18 or higher)
- npm or yarn

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd KaptureHQ-Soft

# Install dependencies
npm install

# Start development server
npm run dev
```

### Available Scripts

- `npm run dev` - Start development mode (Vite + Electron)
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run pack` - Package app for distribution
- `npm run dist` - Build and create distributable packages

## Getting Started

1. **Create or Open a Vault**: When you first launch KaptureHQ, you'll see the vault selector. You can either:
   - Create a new vault by clicking "Create New Vault" and selecting a directory
   - Open an existing vault by clicking "Open Existing Vault"

2. **Create Pages**: Use the sidebar buttons to create new content:
   - "New Page" creates a markdown page for writing
   - "New Database" creates a structured database table

3. **Edit Content**:
   - **Pages**: Use the Edit/Preview toggle to switch between editing and preview modes
   - **Markdown Formatting**: Use the toolbar buttons or keyboard shortcuts for formatting
   - **Databases**: Click on any cell to edit it inline, use the + buttons to add rows/columns

4. **Markdown Shortcuts**:
   - `Ctrl+B` - Bold text
   - `Ctrl+I` - Italic text
   - `Ctrl+1/2/3` - Headings
   - `Ctrl+K` - Insert link
   - `Ctrl+Shift+8` - Bullet list
   - `Ctrl+Shift+7` - Numbered list
   - `Ctrl+S` - Save file
   - Auto-continue lists by pressing Enter

5. **Page Management**: All pages are stored as files in your vault folder and automatically saved when you make changes. The sidebar shows all your pages with appropriate icons (📄 for markdown, 🗃️ for databases).

## Project Structure

```
KaptureHQ-Soft/
├── electron/           # Electron main process
│   ├── main.ts        # Main Electron process
│   └── preload.ts     # Preload script for IPC
├── src/               # React application
│   ├── components/    # React components
│   │   ├── ui/       # shadcn/ui components
│   │   ├── DatabaseTable.tsx
│   │   ├── MainContent.tsx
│   │   ├── MainLayout.tsx
│   │   ├── Sidebar.tsx
│   │   ├── ThemeProvider.tsx
│   │   └── VaultSelector.tsx
│   ├── lib/          # Utility functions
│   ├── types/        # TypeScript type definitions
│   └── App.tsx       # Main React component
├── dist/             # Built application
└── release/          # Packaged distributions

# Example Vault Structure:
MyVault/
├── Welcome.md         # Markdown page
├── Project Ideas.md   # Markdown page
├── Tasks.csv         # Database page
└── Contacts.csv      # Database page
```

## Development Phases

### ✅ Phase 1: Project Setup & Foundation
- [x] Initialize Electron + React project structure
- [x] Configure TypeScript, TailwindCSS, and shadcn/ui
- [x] Set up basic IPC communication
- [x] Create project structure and configuration files
- [x] Complete dependency installation and initial testing
- [x] Implement vault management (create, open, select)
- [x] Set up file system integration
- [x] Create basic UI components (Button, Input, Dialog, Table, Textarea)
- [x] Implement markdown editor with edit/preview modes
- [x] Create database table editor with CSV parsing
- [x] Add file creation dialogs and improved sidebar

### ✅ Phase 2: Basic UI Framework (Completed)
- [x] Implement main application layout
- [x] Create vault selector screen
- [x] Set up routing/navigation system
- [x] Implement theme provider

### ✅ Phase 3: Vault System Implementation (Completed)
- [x] File system integration
- [x] Vault management functionality
- [x] Sidebar vault explorer
- [x] Basic file operations

### ✅ Phase 4: Enhanced Markdown System (Completed)
- [x] Rich markdown editor with formatting toolbar
- [x] Keyboard shortcuts for all formatting options
- [x] Auto-completion for lists and formatting
- [x] Edit/Preview mode toggle
- [x] Syntax highlighting and monospace font
- [ ] Internal linking support (Future enhancement)

### ✅ Phase 5: Database Sheet System (Completed)
- [x] CSV parsing and management
- [x] Database table view
- [x] Basic column editing support
- [ ] Advanced column types (select, reference) (Future enhancement)

### 🚀 Phase 6: Advanced Features (Future)
- [ ] Row detail modal
- [ ] Comments and activity system
- [ ] Settings and preferences
- [ ] Advanced markdown features (wiki links, backlinks)
- [ ] Database relationships and references
- [ ] Search functionality
- [ ] Export/import features

## Contributing

This project is currently in early development. Please refer to the blueprint.md file for detailed feature specifications.

## License

MIT License - see LICENSE file for details.
