# Blueprint: <PERSON><PERSON><PERSON><PERSON><PERSON> (Obsidian + Notion Hybrid Clone)

> **Development Status**: 🚧 In Progress - Phase 1: Project Setup & Foundation

## Overview

KaptureHQ is a desktop application inspired by Obsidian and Notion. It combines a local-first markdown vault system with a flexible relational database feature. Built using **React** and **Electron.js**, styled with **shadcn/ui**, **TailwindCSS**, and **Radix UI**, KaptureHQ provides an intuitive UI similar to Obsidian, with the power of structured data views like Notion.

---

## 1. Vault System

### 1.1 Vault Management

* Create new vault (local directory)
* Open existing vault
* Store vault list in app config (e.g., JSON)

### 1.2 Folder and File Structure

```
/VaultName/
├── Notes/
│   ├── note1.md
│   └── note2.md
├── Databases/
│   ├── Tasks.csv
│   ├── People.csv
```

### 1.3 Folder Management

* Create, rename, delete folders
* Drag and drop files/folders
* Nested folder structure support

### 1.4 Markdown File Management

* Create, edit, rename, delete `.md` files
* Edit in markdown or WYSIWYG mode
* Support for standard markdown syntax
* Internal linking (wiki-style)
* Backlink support (optional future)

---

## 2. Database Sheets

### 2.1 Sheet Creation & Storage

* Each sheet is stored as a `.csv` file
* First row contains column metadata:

  * Format: `ColumnName::type[:refSheet]`
  * Example: `Assignee::reference:People`, `Status::select:Todo|In Progress|Done`

### 2.2 Supported Column Types (encoded in first row)

* `text`, `number`, `date`, `checkbox`
* `select:<option1|option2|...>`
* `reference:<sheetName>`

### 2.3 Row Operations

* Add, edit, delete rows
* Field validation based on type declared in header
* Select reference value via dropdown/search

### 2.4 References

* Store values as labels or foreign keys
* Render references as linked labels in UI
* Optional: show reverse/backlinks in the referenced sheet

---

## 3. Row Detail View

### 3.1 Structure

* Vertical layout of fields
* Rich UI with field-specific widgets
* Linked sheet preview for references

### 3.2 Comments

* Stored in hidden `__comments__` column (JSON string per row)
* Threaded or flat comments
* UI displays username, timestamp, message

### 3.3 Activity Log

* Stored in hidden `__activity__` column (JSON string per row)
* Log includes field, old/new value, user, timestamp

### 3.4 Sample `__activity__` Column Value

```json
[
  { "event": "Field Updated", "field": "Status", "oldValue": "Todo", "newValue": "In Progress", "timestamp": "2025-05-28T12:00:00Z", "user": "Jane" }
]
```

---

## 4. UI Components

### 4.1 Sidebar (Obsidian-style)

* Vault explorer (tree view)
* Toggle between notes and databases
* Collapsible folder structure

### 4.2 Main Panel

* Markdown editor/preview pane
* Sheet table view
* Row detail modal or drawer

### 4.3 Detail Modal

* Vertical form layout for row fields
* Comment section with timestamp
* Activity timeline with changelog

### 4.4 Settings

* Dark/light mode
* Default vault path
* Font size/theme customization

### 4.5 UI Libraries

* **TailwindCSS** for base utility styles
* **shadcn/ui** for accessible and themeable components
* **Radix UI** for headless and composable primitives used in custom UI design

---

## 5. Screen-wise Flow and UI Explanation

### 5.1 Home / Vault Selector

* **UI**: Simple modal or welcome screen listing all known vaults
* **Actions**: Create new vault, open existing, or manage vaults
* **Design**: Clean panel with folder picker, buttons for quick action

### 5.2 Main Layout (Post Vault Open)

* **UI Layout**: Three-panel structure

  * **Left**: Sidebar (vault explorer)
  * **Center**: Active editor/view (markdown or table view)
  * **Right (Optional)**: Inspector / Detail view / Backlinks

### 5.3 Notes View

* **UI**: Markdown file editor or preview toggle
* **Features**: WYSIWYG editor, markdown toolbar, internal links, backlink preview popup

### 5.4 Database Sheet View

* **UI**: Table/grid component similar to Notion’s database view
* **Features**: Column sorting, filtering (optional), inline editing
* **Interactions**: Clicking a row opens detail view modal

### 5.5 Row Detail Modal

* **UI**: Modal or drawer

  * Header with row ID or title
  * Fields shown in form view
  * Reference previews for linked fields
  * Comment thread below fields
  * Timeline section at the bottom

### 5.6 Settings Panel

* **UI**: Modal or side drawer
* **Options**: Theme, font size, vault default, app preferences

---

## 6. Technical Considerations

### 6.1 Local Storage

* Use Node.js file system API (Electron environment)
* All data encoded in CSV files
* No external `.meta.json` or `/activity/` folder required

### 6.2 Tech Stack

* **Platform**: Desktop
* **Framework**: Electron.js
* **Frontend**: React
* **Styling**: TailwindCSS + shadcn/ui + Radix UI
* **Data Storage**: Local filesystem (CSV only)

---

## 7. Future Features (Optional)

* Graph view for notes (like Obsidian)
* Full-text search across notes/sheets
* Plugin system
* Encryption for sensitive data
* Sync via Git or Cloud (optional/local toggle)
