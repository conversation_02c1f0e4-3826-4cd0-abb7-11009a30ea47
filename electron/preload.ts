import { contextBridge, ipc<PERSON>enderer } from 'electron'

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // File system operations
  selectDirectory: () => ipcRenderer.invoke('select-directory'),
  readDirectory: (dirPath: string) => ipcRenderer.invoke('read-directory', dirPath),
  readFile: (filePath: string) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath: string, content: string) => ipcRenderer.invoke('write-file', filePath, content),
  createDirectory: (dirPath: string) => ipcRenderer.invoke('create-directory', dirPath),
  deleteFile: (filePath: string) => ipcRenderer.invoke('delete-file', filePath),
  deleteDirectory: (dirPath: string) => ipcRenderer.invoke('delete-directory', dirPath),
  moveFile: (sourcePath: string, targetPath: string) => ipcRenderer.invoke('move-file', sourcePath, targetPath),

  // Menu event listeners
  onMenuNewVault: (callback: () => void) => ipcRenderer.on('menu-new-vault', callback),
  onMenuOpenVault: (callback: () => void) => ipcRenderer.on('menu-open-vault', callback),
  onMenuNewNote: (callback: () => void) => ipcRenderer.on('menu-new-note', callback),
  onMenuNewDatabase: (callback: () => void) => ipcRenderer.on('menu-new-database', callback),

  // Remove listeners
  removeAllListeners: (channel: string) => ipcRenderer.removeAllListeners(channel),
})

// Type definitions for the exposed API
export interface ElectronAPI {
  selectDirectory: () => Promise<string | null>
  readDirectory: (dirPath: string) => Promise<Array<{
    name: string
    isDirectory: boolean
    path: string
  }>>
  readFile: (filePath: string) => Promise<string>
  writeFile: (filePath: string, content: string) => Promise<boolean>
  createDirectory: (dirPath: string) => Promise<boolean>
  deleteFile: (filePath: string) => Promise<boolean>
  deleteDirectory: (dirPath: string) => Promise<boolean>
  moveFile: (sourcePath: string, targetPath: string) => Promise<boolean>
  onMenuNewVault: (callback: () => void) => void
  onMenuOpenVault: (callback: () => void) => void
  onMenuNewNote: (callback: () => void) => void
  onMenuNewDatabase: (callback: () => void) => void
  removeAllListeners: (channel: string) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
