{"compilerOptions": {"target": "ES2020", "lib": ["ES2020"], "module": "CommonJS", "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": false, "outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "include": ["electron/**/*"], "exclude": ["node_modules", "dist", "src"]}