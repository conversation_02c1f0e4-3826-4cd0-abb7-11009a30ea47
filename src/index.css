@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 9%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 9%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;
    --primary: 0 0% 9%;
    --primary-foreground: 0 0% 98%;
    --secondary: 0 0% 96%;
    --secondary-foreground: 0 0% 9%;
    --muted: 0 0% 96%;
    --muted-foreground: 0 0% 45%;
    --accent: 0 0% 96%;
    --accent-foreground: 0 0% 9%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 90%;
    --input: 0 0% 90%;
    --ring: 0 0% 9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Obsidian-style cursor blinking animations */
@keyframes cm-blink {
  50% {
    opacity: 0;
  }
}

@keyframes cm-blink2 {
  50% {
    opacity: 0;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-md;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* App-specific styles - Notion-like layout */
.app-layout {
  height: 100vh;
  display: grid;
  grid-template-columns: 240px 1fr;
  grid-template-rows: 1fr;
  @apply bg-background;
}

.sidebar {
  @apply border-r border-border/50 bg-muted/20;
  backdrop-filter: blur(8px);
}

.main-content {
  @apply bg-background overflow-hidden;
}

/* Rich Markdown Editor - Modern style */
.rich-markdown-editor {
  @apply bg-background;
}

.rich-editor-textarea {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: hsl(var(--foreground));
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  tab-size: 2;
}

.rich-editor-textarea:focus {
  outline: none;
  box-shadow: none;
}

/* Legacy markdown editor styles */
.markdown-editor {
  @apply w-full h-full resize-none border-none outline-none bg-transparent font-mono text-sm leading-relaxed;
  font-family: 'JetBrains Mono', 'Fira Code', 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  tab-size: 2;
}

.markdown-editor:focus {
  @apply outline-none shadow-none;
}

.markdown-preview {
  @apply max-w-none text-sm leading-relaxed;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.markdown-preview h1 {
  @apply text-2xl font-bold mt-6 mb-4 pb-2 border-b border-border;
}

.markdown-preview h2 {
  @apply text-xl font-semibold mt-5 mb-3;
}

.markdown-preview h3 {
  @apply text-lg font-semibold mt-4 mb-2;
}

.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  @apply font-semibold mt-3 mb-2;
}

.markdown-preview p {
  @apply mb-3;
}

.markdown-preview ul,
.markdown-preview ol {
  @apply mb-3 pl-6;
}

.markdown-preview li {
  @apply mb-1;
}

.markdown-preview code {
  @apply bg-muted px-1 py-0.5 rounded text-sm font-mono;
}

.markdown-preview pre {
  @apply bg-muted p-4 rounded-lg overflow-x-auto mb-4;
}

.markdown-preview pre code {
  @apply bg-transparent p-0;
}

.markdown-preview blockquote {
  @apply border-l-4 border-primary pl-4 italic text-muted-foreground mb-4;
}

.markdown-preview a {
  @apply text-primary underline hover:no-underline;
}

.markdown-preview strong {
  @apply font-semibold;
}

.markdown-preview em {
  @apply italic;
}

/* Tree view styles - Notion-like */
.tree-item {
  @apply flex items-center gap-2 px-2 py-1.5 rounded-md hover:bg-muted/50 cursor-pointer transition-colors;
  font-size: 14px;
}

.tree-item.selected {
  @apply bg-muted/70;
}

.tree-item-icon {
  @apply w-4 h-4 text-muted-foreground flex-shrink-0;
}

.tree-item-text {
  @apply text-sm truncate text-foreground;
  font-weight: 400;
}

/* Drag and drop styles */
.tree-item[draggable="true"] {
  @apply cursor-move;
}

.tree-item.drop-target {
  @apply relative;
}

.tree-item.drop-target:hover {
  @apply bg-primary/10 border border-primary/30;
}

.tree-item[draggable="true"]:active {
  @apply opacity-50;
}

/* Obsidian-style Editor */
.obsidian-editor {
  @apply bg-background text-foreground;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.obsidian-header {
  @apply sticky top-0 z-10;
}

.obsidian-title {
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  margin: 0;
  padding: 0;
}

.obsidian-title-input {
  color: var(--foreground);
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  margin: 0;
  padding: 0;
}

.obsidian-title-input:focus {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  ring: none !important;
}

.obsidian-textarea {
  @apply text-foreground;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  tab-size: 2;
}

.obsidian-textarea:focus {
  @apply outline-none ring-0 border-none shadow-none;
}

.obsidian-textarea::placeholder {
  @apply text-muted-foreground;
}

/* Obsidian-style Preview */
.obsidian-preview {
  @apply text-foreground;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.obsidian-preview .obsidian-h1 {
  @apply text-2xl font-bold mt-8 mb-4 pb-2 border-b border-border cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
}

.obsidian-preview .obsidian-h2 {
  @apply text-xl font-semibold mt-6 mb-3 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
}

.obsidian-preview .obsidian-h3 {
  @apply text-lg font-semibold mt-5 mb-2 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
}

.obsidian-preview .obsidian-p {
  @apply mb-4 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
}

.obsidian-preview .obsidian-li {
  @apply mb-1 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
  list-style-type: disc;
  margin-left: 1.5rem;
}

.obsidian-preview .obsidian-li-ordered {
  @apply mb-1 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
  list-style-type: decimal;
  margin-left: 1.5rem;
}

.obsidian-preview .obsidian-quote {
  @apply border-l-4 border-primary pl-4 italic text-muted-foreground mb-4 cursor-pointer hover:bg-muted/10 rounded px-2 py-1 -mx-2 -my-1 transition-colors;
}

.obsidian-preview strong {
  @apply font-semibold;
}

.obsidian-preview em {
  @apply italic;
}

.obsidian-preview code {
  @apply bg-muted px-1.5 py-0.5 rounded text-sm font-mono;
}

.obsidian-preview a {
  @apply text-primary underline hover:no-underline;
}

/* Obsidian-style Seamless Editor */
.seamless-editor {
  @apply text-foreground;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  tab-size: 4;
  letter-spacing: 0.005em;
  word-spacing: 0.05em;
}

.seamless-editor:focus {
  @apply outline-none ring-0 border-none shadow-none;
}

.seamless-editor::placeholder {
  @apply text-muted-foreground/60;
  font-style: italic;
}

.seamless-preview {
  @apply text-foreground;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  min-height: 600px;
  letter-spacing: 0.005em;
  word-spacing: 0.05em;
}

.seamless-preview:hover {
  background-color: hsl(var(--muted) / 0.03);
  border-radius: 0.5rem;
  transition: all 0.2s ease;
}

/* Obsidian-style markdown typography */
.seamless-preview h1 {
  font-size: 2em;
  font-weight: 700;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  padding-bottom: 0.3em;
  border-bottom: 1px solid var(--border);
  color: var(--foreground);
}

.seamless-preview h2 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

.seamless-preview h3 {
  font-size: 1.25em;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

.seamless-preview h4 {
  font-size: 1em;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

.seamless-preview h5 {
  font-size: 0.875em;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--foreground);
}

.seamless-preview h6 {
  font-size: 0.85em;
  font-weight: 600;
  line-height: 1.25;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--muted-foreground);
}

.seamless-preview p {
  margin-top: 0;
  margin-bottom: 1em;
  line-height: 1.5;
}

.seamless-preview strong {
  font-weight: 600;
  color: var(--foreground);
}

.seamless-preview em {
  font-style: italic;
  color: var(--foreground);
}

.seamless-preview del {
  text-decoration: line-through;
  color: var(--muted-foreground);
  opacity: 0.7;
}

.seamless-preview u {
  text-decoration: underline;
  text-decoration-color: var(--muted-foreground);
}

.seamless-preview code {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
  font-weight: 400;
}

.seamless-preview pre {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 1em;
  border-radius: 6px;
  overflow-x: auto;
  margin: 1em 0;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
  line-height: 1.45;
}

.seamless-preview blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1em;
  margin: 1em 0;
  color: var(--muted-foreground);
  font-style: italic;
  background-color: var(--muted)/10;
  border-radius: 0 4px 4px 0;
}

.seamless-preview ul, .seamless-preview ol {
  margin: 1em 0;
  padding-left: 2em;
}

.seamless-preview li {
  margin: 0.25em 0;
  line-height: 1.5;
}

.seamless-preview ul li {
  list-style-type: disc;
}

.seamless-preview ol li {
  list-style-type: decimal;
}

.seamless-preview ul ul li {
  list-style-type: circle;
}

.seamless-preview ul ul ul li {
  list-style-type: square;
}

.seamless-preview a {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-color: var(--primary)/50;
  transition: all 0.2s ease;
}

.seamless-preview a:hover {
  text-decoration: none;
  color: var(--primary);
  opacity: 0.8;
}

.seamless-preview hr {
  border: none;
  border-top: 1px solid var(--border);
  margin: 2em 0;
}

.seamless-preview table {
  border-collapse: collapse;
  margin: 1em 0;
  width: 100%;
}

.seamless-preview th, .seamless-preview td {
  border: 1px solid var(--border);
  padding: 0.5em 1em;
  text-align: left;
}

.seamless-preview th {
  background-color: var(--muted);
  font-weight: 600;
}

/* First paragraph after headers should have no top margin */
.seamless-preview h1 + p,
.seamless-preview h2 + p,
.seamless-preview h3 + p,
.seamless-preview h4 + p,
.seamless-preview h5 + p,
.seamless-preview h6 + p {
  margin-top: 0;
}

/* Last element should have no bottom margin */
.seamless-preview > *:last-child {
  margin-bottom: 0;
}

/* First element should have no top margin */
.seamless-preview > *:first-child {
  margin-top: 0;
}

/* Obsidian-style Unified Editor */
.obsidian-unified-editor {
  /* Base typography matching Obsidian exactly */
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  letter-spacing: 0.005em;
  word-spacing: 0.05em;
  tab-size: 4;
  color: var(--foreground);

  /* Remove all input styling */
  background: transparent !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  resize: none !important;

  /* Text behavior */
  white-space: pre-wrap;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

.obsidian-unified-editor:focus {
  outline: none !important;
  border: none !important;
  box-shadow: none !important;
  background: transparent !important;
}

.obsidian-unified-editor::placeholder {
  color: var(--muted-foreground);
  opacity: 0.6;
  font-style: italic;
}

/* Obsidian-style text selection */
.obsidian-unified-editor::selection {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

.obsidian-unified-editor::-moz-selection {
  background-color: var(--primary);
  color: var(--primary-foreground);
}

/* Obsidian syntax styling */
.obsidian-syntax {
  color: var(--muted-foreground);
  opacity: 0.5;
  font-weight: 400;
}

/* Headers */
.obsidian-header {
  display: flex;
  align-items: baseline;
  margin: 1.5em 0 0.5em 0;
}

.obsidian-h1 .obsidian-header-content {
  font-size: 2em;
  font-weight: 700;
  line-height: 1.25;
  color: var(--foreground);
}

.obsidian-h2 .obsidian-header-content {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--foreground);
}

.obsidian-h3 .obsidian-header-content {
  font-size: 1.25em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--foreground);
}

.obsidian-h4 .obsidian-header-content {
  font-size: 1em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--foreground);
}

.obsidian-h5 .obsidian-header-content {
  font-size: 0.875em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--foreground);
}

.obsidian-h6 .obsidian-header-content {
  font-size: 0.85em;
  font-weight: 600;
  line-height: 1.25;
  color: var(--muted-foreground);
}

/* Paragraphs */
.obsidian-paragraph {
  margin: 0 0 1em 0;
  line-height: 1.5;
  color: var(--foreground);
}

/* Lists */
.obsidian-list-item {
  display: flex;
  align-items: flex-start;
  margin: 0.25em 0;
  line-height: 1.5;
}

.obsidian-list-content {
  color: var(--foreground);
}

/* Blockquotes */
.obsidian-blockquote {
  display: flex;
  align-items: flex-start;
  margin: 1em 0;
  padding-left: 1em;
  border-left: 4px solid var(--primary);
  background-color: hsl(var(--muted) / 0.1);
  border-radius: 0 4px 4px 0;
}

.obsidian-quote-content {
  color: var(--muted-foreground);
  font-style: italic;
}

/* Inline formatting */
.obsidian-bold .obsidian-bold-content {
  font-weight: 600;
  color: var(--foreground);
}

.obsidian-italic .obsidian-italic-content {
  font-style: italic;
  color: var(--foreground);
}

.obsidian-strikethrough .obsidian-strikethrough-content {
  text-decoration: line-through;
  color: var(--muted-foreground);
  opacity: 0.7;
}

.obsidian-underline .obsidian-underline-content {
  text-decoration: underline;
  text-decoration-color: var(--muted-foreground);
}

.obsidian-code {
  background-color: var(--muted);
  border-radius: 3px;
  padding: 0.1em 0.2em;
}

.obsidian-code .obsidian-code-content {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
  color: var(--foreground);
}

.obsidian-link .obsidian-link-text {
  color: var(--primary);
  text-decoration: underline;
  text-decoration-color: var(--primary)/50;
}

.obsidian-link .obsidian-link-url {
  color: var(--muted-foreground);
  font-size: 0.9em;
}

/* Horizontal rules */
.obsidian-hr {
  margin: 2em 0;
  text-align: center;
}

.obsidian-hr .obsidian-syntax {
  color: var(--border);
  font-weight: 300;
}

/* Empty lines */
.obsidian-empty-line {
  height: 1.5em;
  line-height: 1.5;
}

/* Obsidian-style unified editor */
.obsidian-unified-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  width: 100%;
  min-height: 600px;
  padding: 0;
  margin: 0;

  /* Smooth scrolling */
  scroll-behavior: smooth;

  /* Better text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* Selection styling */
  ::selection {
    background-color: var(--accent);
  }

  /* Placeholder styling */
  &::placeholder {
    color: var(--muted-foreground);
    opacity: 0.6;
  }

  /* Focus state */
  &:focus {
    outline: none;
    border: none;
  }
}

/* Remove any input-like styling */
.seamless-editor {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.seamless-editor:focus {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  ring: none !important;
}

/* Notion-style table */
.notion-table {
  @apply w-full border-collapse;
  border-spacing: 0;
}

.notion-table th {
  @apply text-left font-medium text-sm text-muted-foreground;
  border-bottom: 1px solid hsl(var(--border) / 0.5);
  padding: 8px 12px;
}

.notion-table td {
  @apply text-sm;
  border-bottom: 1px solid hsl(var(--border) / 0.3);
}

.notion-table tr:hover {
  @apply bg-muted/20;
}

/* Legacy table styles */
.database-table {
  @apply w-full border-collapse;
}

.database-table th,
.database-table td {
  @apply border border-border px-3 py-2 text-left;
}

.database-table th {
  @apply bg-muted font-medium;
}

.database-table tr:hover {
  @apply bg-muted/50;
}

/* CodeMirror Integration Styles */
.codemirror-editor {
  @apply w-full h-full;
}

.codemirror-editor .cm-editor {
  @apply w-full h-full;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
  font-size: 16px !important;
  line-height: 1.5 !important;
}

.codemirror-editor .cm-content {
  @apply text-foreground;
  padding: 0 !important;
  min-height: 600px !important;
}

.codemirror-editor .cm-focused {
  outline: none !important;
}

.codemirror-editor .cm-line {
  padding: 0 !important;
  line-height: 1.5 !important;
}

.codemirror-editor .cm-cursor {
  border-left-color: var(--foreground) !important;
}

/* CodeMirror Markdown Syntax Highlighting */
.codemirror-editor .cm-header {
  font-weight: bold !important;
  color: var(--foreground) !important;
}

.codemirror-editor .cm-header1 {
  font-size: 2em !important;
  font-weight: 700 !important;
}

.codemirror-editor .cm-header2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
}

.codemirror-editor .cm-header3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
}

.codemirror-editor .cm-strong {
  font-weight: 600 !important;
  color: var(--foreground) !important;
}

.codemirror-editor .cm-emphasis {
  font-style: italic !important;
  color: var(--foreground) !important;
}

.codemirror-editor .cm-strikethrough {
  text-decoration: line-through !important;
  color: var(--muted-foreground) !important;
}

.codemirror-editor .cm-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
}

.codemirror-editor .cm-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
}

.codemirror-editor .cm-quote {
  color: var(--muted-foreground) !important;
  font-style: italic !important;
}

.codemirror-editor .cm-list {
  color: var(--foreground) !important;
}

/* CodeMirror Selection */
.codemirror-editor .cm-selectionBackground {
  background-color: var(--accent) !important;
}

.codemirror-editor .cm-focused .cm-selectionBackground {
  background-color: var(--accent) !important;
}

/* CodeMirror Search */
.codemirror-editor .cm-searchMatch {
  background-color: var(--accent) !important;
  outline: 1px solid var(--ring) !important;
}

.codemirror-editor .cm-searchMatch.cm-searchMatch-selected {
  background-color: var(--accent) !important;
}

/* Hybrid Markdown Editor */
.hybrid-markdown-editor {
  @apply w-full h-full;
}

.hybrid-markdown-editor .codemirror-wrapper {
  @apply w-full h-full;
}

.hybrid-markdown-editor .cm-editor {
  @apply w-full h-full;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
}

.hybrid-markdown-editor .cm-content {
  @apply text-foreground;
  padding: 0 !important;
  min-height: 600px !important;
}

.hybrid-markdown-editor .cm-focused {
  outline: none !important;
}

.hybrid-markdown-editor .cm-line {
  padding: 0 !important;
  line-height: 1.6 !important;
}

/* Enhanced markdown syntax highlighting for hybrid editor */
.hybrid-markdown-editor .cm-header {
  font-weight: bold !important;
  color: var(--foreground) !important;
  margin: 0.5em 0 !important;
}

.hybrid-markdown-editor .cm-header1 {
  font-size: 2em !important;
  font-weight: 700 !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: 0.3em !important;
}

.hybrid-markdown-editor .cm-header2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: 0.3em !important;
}

.hybrid-markdown-editor .cm-header3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
}

.hybrid-markdown-editor .cm-strong {
  font-weight: 600 !important;
  color: var(--foreground) !important;
}

.hybrid-markdown-editor .cm-emphasis {
  font-style: italic !important;
  color: var(--foreground) !important;
}

.hybrid-markdown-editor .cm-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
}

.hybrid-markdown-editor .cm-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
}

.hybrid-markdown-editor .cm-quote {
  color: var(--muted-foreground) !important;
  font-style: italic !important;
  border-left: 4px solid var(--border) !important;
  padding-left: 1em !important;
  margin-left: 0 !important;
}

.hybrid-markdown-editor .cm-list {
  color: var(--foreground) !important;
  margin: 0.5em 0 !important;
}

/* Markdoc Hybrid Editor */
.markdoc-hybrid-editor {
  @apply w-full h-full;
}

.markdoc-hybrid-editor .codemirror-wrapper {
  @apply w-full h-full;
}

.markdoc-hybrid-editor .cm-editor {
  @apply w-full h-full;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
}

.markdoc-hybrid-editor .cm-content {
  @apply text-foreground;
  padding: 0 !important;
  min-height: 600px !important;
}

.markdoc-hybrid-editor .cm-focused {
  outline: none !important;
}

.markdoc-hybrid-editor .cm-line {
  padding: 0 !important;
  line-height: 1.6 !important;
}

/* Markdoc-style markdown rendering */
.markdoc-hybrid-editor .cm-header {
  font-weight: bold !important;
  color: var(--foreground) !important;
  display: block !important;
  margin: 1em 0 0.5em 0 !important;
}

.markdoc-hybrid-editor .cm-header1 {
  font-size: 2em !important;
  font-weight: 700 !important;
  border-bottom: 2px solid var(--border) !important;
  padding-bottom: 0.3em !important;
  margin-bottom: 0.5em !important;
}

.markdoc-hybrid-editor .cm-header2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: 0.3em !important;
  margin-bottom: 0.5em !important;
}

.markdoc-hybrid-editor .cm-header3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
  margin-bottom: 0.5em !important;
}

.markdoc-hybrid-editor .cm-strong {
  font-weight: 600 !important;
  color: var(--foreground) !important;
}

.markdoc-hybrid-editor .cm-emphasis {
  font-style: italic !important;
  color: var(--foreground) !important;
}

.markdoc-hybrid-editor .cm-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 6px !important;
  border-radius: 4px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
  border: 1px solid var(--border) !important;
}

.markdoc-hybrid-editor .cm-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

.markdoc-hybrid-editor .cm-quote {
  color: var(--muted-foreground) !important;
  font-style: italic !important;
  border-left: 4px solid var(--primary) !important;
  padding-left: 1em !important;
  margin: 1em 0 !important;
  background-color: var(--muted/20) !important;
  padding: 0.5em 0 0.5em 1em !important;
}

.markdoc-hybrid-editor .cm-list {
  color: var(--foreground) !important;
  margin: 0.5em 0 !important;
  padding-left: 1.5em !important;
}

/* Enhanced selection and cursor */
.markdoc-hybrid-editor .cm-selectionBackground {
  background-color: var(--accent) !important;
}

.markdoc-hybrid-editor .cm-focused .cm-selectionBackground {
  background-color: var(--accent) !important;
}

.markdoc-hybrid-editor .cm-cursor {
  border-left-color: var(--primary) !important;
  border-left-width: 2px !important;
}

/* Working Hybrid Rendering Styles */
.markdoc-hybrid-editor .hybrid-header-1 {
  font-size: 2em !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 0.5em 0 !important;
  border-bottom: 2px solid var(--border) !important;
  padding-bottom: 0.3em !important;
  display: block !important;
}

.markdoc-hybrid-editor .hybrid-header-2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 0.4em 0 !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: 0.3em !important;
  display: block !important;
}

.markdoc-hybrid-editor .hybrid-header-3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0.3em 0 !important;
  display: block !important;
}

.markdoc-hybrid-editor .hybrid-header-4 {
  font-size: 1.1em !important;
  font-weight: 600 !important;
  margin: 0.2em 0 !important;
  display: block !important;
}

.markdoc-hybrid-editor .hybrid-header-5 {
  font-size: 1em !important;
  font-weight: 600 !important;
  margin: 0.2em 0 !important;
  display: block !important;
}

.markdoc-hybrid-editor .hybrid-header-6 {
  font-size: 0.9em !important;
  font-weight: 600 !important;
  margin: 0.1em 0 !important;
  display: block !important;
}

/* Bold text styling */
.markdoc-hybrid-editor .hybrid-bold {
  color: var(--muted-foreground) !important;
}

.markdoc-hybrid-editor .hybrid-bold-content {
  font-weight: 700 !important;
  color: var(--foreground) !important;
}

/* Italic text styling */
.markdoc-hybrid-editor .hybrid-italic {
  color: var(--muted-foreground) !important;
}

.markdoc-hybrid-editor .hybrid-italic-content {
  font-style: italic !important;
  color: var(--foreground) !important;
}

/* Code styling */
.markdoc-hybrid-editor .hybrid-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
  border: 1px solid var(--border) !important;
}

/* Links */
.markdoc-hybrid-editor .hybrid-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
  cursor: pointer !important;
}

/* Lists */
.markdoc-hybrid-editor .hybrid-list-item {
  padding-left: 1.5em !important;
  margin: 0.2em 0 !important;
}

/* Blockquotes */
.markdoc-hybrid-editor .hybrid-blockquote {
  border-left: 4px solid var(--primary) !important;
  padding-left: 1em !important;
  margin: 0.5em 0 !important;
  background-color: var(--muted/10) !important;
  color: var(--muted-foreground) !important;
  font-style: italic !important;
}

/* Simple Hybrid Editor */
.simple-hybrid-editor {
  @apply w-full h-full;
}

.simple-hybrid-editor .codemirror-wrapper {
  @apply w-full h-full;
}

.simple-hybrid-editor .cm-editor {
  @apply w-full h-full;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
}

.simple-hybrid-editor .cm-content {
  @apply text-foreground;
  padding: 0 !important;
  min-height: 600px !important;
}

.simple-hybrid-editor .cm-focused {
  outline: none !important;
}

.simple-hybrid-editor .cm-line {
  padding: 0 !important;
  line-height: 1.6 !important;
}

/* Hybrid styling classes */
.simple-hybrid-editor .hybrid-h1 {
  font-size: 2em !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
  margin: 0.5em 0 !important;
  border-bottom: 2px solid var(--border) !important;
  padding-bottom: 0.3em !important;
}

.simple-hybrid-editor .hybrid-h2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
  margin: 0.4em 0 !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: 0.3em !important;
}

.simple-hybrid-editor .hybrid-h3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
  margin: 0.3em 0 !important;
}

.simple-hybrid-editor .hybrid-bold {
  font-weight: 700 !important;
}

.simple-hybrid-editor .hybrid-italic {
  font-style: italic !important;
}

.simple-hybrid-editor .hybrid-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
  border: 1px solid var(--border) !important;
}

/* Working Hybrid Editor */
.working-hybrid-editor {
  @apply w-full h-full;
}

.working-hybrid-editor .hybrid-container {
  position: relative;
  width: 100%;
  min-height: 600px;
}

.working-hybrid-editor .hybrid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  color: transparent;
  z-index: 1;
}

.working-hybrid-editor .hybrid-textarea {
  position: relative;
  width: 100%;
  min-height: 600px;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  color: var(--foreground);
  z-index: 2;
  caret-color: var(--foreground);
}

/* Hybrid styling for overlay */
.working-hybrid-editor .hybrid-h1 {
  font-size: 2em;
  font-weight: 700;
  line-height: 1.2;
  margin: 0.5em 0;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-h2 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.3;
  margin: 0.4em 0;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-h3 {
  font-size: 1.25em;
  font-weight: 600;
  line-height: 1.4;
  margin: 0.3em 0;
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-line {
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-bold-content {
  font-weight: 700;
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-bold-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
}

.working-hybrid-editor .hybrid-italic-content {
  font-style: italic;
  color: var(--foreground);
}

.working-hybrid-editor .hybrid-italic-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
}

.working-hybrid-editor .hybrid-code-content {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
}

.working-hybrid-editor .hybrid-code-marker {
  color: var(--muted-foreground);
  background-color: var(--muted);
  padding: 2px 2px;
  border-radius: 3px;
  opacity: 0.7;
}

/* Basic Hybrid Editor - Simple approach that actually works */
.basic-hybrid-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  width: 100%;
  min-height: 600px;
  padding: 0;
  margin: 0;

  /* Better text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;

  /* Focus state */
  &:focus {
    outline: none;
    border: none;
  }
}

/* Obsidian-style Title Styling */
.obsidian-title {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
  line-height: 1.2;
  color: var(--foreground);
  letter-spacing: -0.025em;
  margin: 0;
  padding: 8px 12px;
  border-radius: 6px;
  transition: all 0.15s ease-in-out;
  word-break: break-word;
}

.obsidian-title:hover {
  background-color: hsl(var(--muted) / 0.1);
  transform: translateY(-1px);
}

.obsidian-title:active {
  transform: translateY(0);
}

.obsidian-title-input {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  font-size: 2.25rem; /* 36px */
  font-weight: 700;
  line-height: 1.2;
  color: var(--foreground);
  letter-spacing: -0.025em;
  background: transparent;
  border: 2px solid hsl(var(--primary) / 0.2);
  border-radius: 6px;
  padding: 8px 12px;
  margin: 0;
  outline: none;
  transition: all 0.15s ease-in-out;
  word-break: break-word;
}

.obsidian-title-input:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px hsl(var(--primary) / 0.1);
  background-color: var(--background);
}

.obsidian-title-input::placeholder {
  color: var(--muted-foreground);
  opacity: 0.6;
}

/* Header section styling */
.obsidian-header {
  background: hsl(var(--background) / 0.95);
  backdrop-filter: blur(8px);
  border-bottom: 1px solid hsl(var(--border) / 0.5);
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 1px 3px 0 hsl(var(--foreground) / 0.1);
}

/* Auto-save indicator improvements */
.auto-save-indicator {
  font-size: 0.875rem;
  font-weight: 500;
}

.auto-save-indicator .saving {
  color: #ea580c; /* orange-600 */
}

.auto-save-indicator .saved {
  color: #16a34a; /* green-600 */
}

.pulse-dot {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* True Hybrid Editor - Shows markdown syntax but renders it visually */
.true-hybrid-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  color: var(--foreground);
  background: transparent;
  border: none;
  outline: none;
  width: 100%;
  min-height: 600px;
  padding: 0;
  margin: 0;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.true-hybrid-editor:focus {
  outline: none;
}

.true-hybrid-editor:empty:before {
  content: attr(data-placeholder);
  color: var(--muted-foreground);
  opacity: 0.6;
  pointer-events: none;
}

/* Obsidian-style Markdown Editor */
.obsidian-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

.obsidian-header {
  border-bottom: 1px solid var(--border);
  background: var(--background);
  position: sticky;
  top: 0;
  z-index: 10;
}

.obsidian-title {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--foreground);
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  transition: color 0.2s ease;
}

.obsidian-title:hover {
  color: var(--muted-foreground);
}

.obsidian-title-input {
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1.2;
  color: var(--foreground);
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  outline: none;
  font-family: inherit;
}

.obsidian-title-input:focus {
  outline: 2px solid var(--ring);
  outline-offset: 2px;
  border-radius: 4px;
}

.obsidian-action-button {
  color: var(--muted-foreground);
  transition: all 0.2s ease;
}

.obsidian-action-button:hover {
  color: var(--foreground);
  background: var(--accent);
}

.obsidian-toolbar {
  border-bottom: 1px solid var(--border);
  background: var(--background);
  position: sticky;
  top: 0;
  z-index: 9;
}

.obsidian-toolbar .btn {
  color: var(--muted-foreground);
  transition: all 0.2s ease;
  border-radius: 4px;
}

.obsidian-toolbar .btn:hover {
  color: var(--foreground);
  background: var(--accent);
}

.obsidian-codemirror-editor {
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
}

.obsidian-codemirror-editor .cm-editor {
  font-family: inherit !important;
  font-size: 16px !important;
  line-height: 1.6 !important;
}

.obsidian-codemirror-editor .cm-content {
  padding: 0 !important;
  min-height: 600px !important;
  font-family: inherit !important;
}

.obsidian-codemirror-editor .cm-focused {
  outline: none !important;
}

.obsidian-codemirror-editor .cm-line {
  padding: 0 !important;
  line-height: 1.6 !important;
  font-family: inherit !important;
}

.obsidian-codemirror-editor .cm-cursor {
  border-left-color: var(--foreground) !important;
}

/* Obsidian-style markdown syntax highlighting */
.obsidian-codemirror-editor .cm-header {
  font-weight: bold !important;
  color: var(--foreground) !important;
}

.obsidian-codemirror-editor .cm-header1 {
  font-size: 2em !important;
  font-weight: 700 !important;
  line-height: 1.2 !important;
}

.obsidian-codemirror-editor .cm-header2 {
  font-size: 1.5em !important;
  font-weight: 600 !important;
  line-height: 1.3 !important;
}

.obsidian-codemirror-editor .cm-header3 {
  font-size: 1.25em !important;
  font-weight: 600 !important;
  line-height: 1.4 !important;
}

.obsidian-codemirror-editor .cm-strong {
  font-weight: 600 !important;
  color: var(--foreground) !important;
}

.obsidian-codemirror-editor .cm-emphasis {
  font-style: italic !important;
  color: var(--foreground) !important;
}

.obsidian-codemirror-editor .cm-code {
  background-color: var(--muted) !important;
  color: var(--foreground) !important;
  padding: 2px 4px !important;
  border-radius: 3px !important;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace !important;
  font-size: 0.85em !important;
}

.obsidian-codemirror-editor .cm-link {
  color: var(--primary) !important;
  text-decoration: underline !important;
}

.obsidian-codemirror-editor .cm-quote {
  color: var(--muted-foreground) !important;
  font-style: italic !important;
  border-left: 4px solid var(--border) !important;
  padding-left: 1em !important;
  margin-left: 0 !important;
}

.obsidian-codemirror-editor .cm-list {
  color: var(--foreground) !important;
}

.obsidian-codemirror-editor .cm-url {
  color: var(--primary) !important;
  text-decoration: underline !important;
}

.obsidian-codemirror-editor .cm-meta {
  color: var(--muted-foreground) !important;
}

.obsidian-codemirror-editor .cm-activeLine {
  background-color: var(--accent) !important;
}

.obsidian-codemirror-editor .cm-selectionBackground {
  background-color: var(--accent) !important;
}

/* Headers - Large and styled but keep # visible */
.true-hybrid-editor .hybrid-h1 {
  font-size: 2em;
  font-weight: 700;
  line-height: 1.2;
  margin: 0.5em 0;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-h2 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.3;
  margin: 0.4em 0;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-h3 {
  font-size: 1.25em;
  font-weight: 600;
  line-height: 1.4;
  margin: 0.3em 0;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 0.2em 0;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-h5 {
  font-size: 1em;
  font-weight: 600;
  margin: 0.2em 0;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-h6 {
  font-size: 0.9em;
  font-weight: 600;
  margin: 0.1em 0;
  color: var(--foreground);
}

/* Bold text - content is bold, markers are subtle */
.true-hybrid-editor .hybrid-bold-content {
  font-weight: 700;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-bold-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  font-weight: normal;
}

/* Italic text - content is italic, markers are subtle */
.true-hybrid-editor .hybrid-italic-content {
  font-style: italic;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-italic-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  font-style: normal;
}

/* Code - content has background, markers are subtle */
.true-hybrid-editor .hybrid-code-content {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
  border: 1px solid var(--border);
}

.true-hybrid-editor .hybrid-code-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  background-color: var(--muted);
  padding: 2px 2px;
  border-radius: 3px;
}

/* Links - styled but syntax visible */
.true-hybrid-editor .hybrid-link-text {
  color: var(--primary);
  text-decoration: underline;
}

.true-hybrid-editor .hybrid-link-url {
  color: var(--muted-foreground);
  text-decoration: none;
}

.true-hybrid-editor .hybrid-link-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
}

/* Lists - styled with proper indentation */
.true-hybrid-editor .hybrid-list-item {
  padding-left: 1.5em;
  margin: 0.2em 0;
  position: relative;
}

.true-hybrid-editor .hybrid-numbered-list-item {
  padding-left: 1.5em;
  margin: 0.2em 0;
  position: relative;
}

/* Blockquotes - styled with border and background */
.true-hybrid-editor .hybrid-blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1em;
  margin: 0.5em 0;
  background-color: hsl(var(--muted) / 0.1);
  color: var(--muted-foreground);
  font-style: italic;
  padding: 0.5em 0 0.5em 1em;
}

/* Paragraphs */
.true-hybrid-editor .hybrid-paragraph {
  margin: 0.5em 0;
  color: var(--foreground);
}

.true-hybrid-editor .hybrid-paragraph:first-child {
  margin-top: 0;
}

.true-hybrid-editor .hybrid-paragraph:last-child {
  margin-bottom: 0;
}

/* Reliable Hybrid Editor - Textarea with styled overlay */
.reliable-hybrid-editor {
  width: 100%;
  height: 100%;
}

.reliable-hybrid-editor .hybrid-container {
  position: relative;
  width: 100%;
  min-height: 600px;
}

.reliable-hybrid-editor .hybrid-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
  white-space: pre-wrap;
  word-wrap: break-word;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  z-index: 1;
  color: transparent; /* Make base text invisible */
}

.reliable-hybrid-editor .hybrid-textarea {
  position: relative;
  width: 100%;
  min-height: 600px;
  background: transparent;
  border: none;
  outline: none;
  resize: none;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
  font-size: 16px;
  line-height: 1.6;
  padding: 0;
  margin: 0;
  color: var(--foreground);
  z-index: 2;
  caret-color: var(--foreground);
}

/* Overlay styling - visible elements */
.reliable-hybrid-editor .overlay-h1 {
  font-size: 2em;
  font-weight: 700;
  line-height: 1.2;
  margin: 0.5em 0;
  border-bottom: 2px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-h2 {
  font-size: 1.5em;
  font-weight: 600;
  line-height: 1.3;
  margin: 0.4em 0;
  border-bottom: 1px solid var(--border);
  padding-bottom: 0.3em;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-h3 {
  font-size: 1.25em;
  font-weight: 600;
  line-height: 1.4;
  margin: 0.3em 0;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-h4 {
  font-size: 1.1em;
  font-weight: 600;
  margin: 0.2em 0;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-h5 {
  font-size: 1em;
  font-weight: 600;
  margin: 0.2em 0;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-h6 {
  font-size: 0.9em;
  font-weight: 600;
  margin: 0.1em 0;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-bold-content {
  font-weight: 700;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-bold-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  font-weight: normal;
}

.reliable-hybrid-editor .overlay-italic-content {
  font-style: italic;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-italic-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  font-style: normal;
}

.reliable-hybrid-editor .overlay-code-content {
  background-color: var(--muted);
  color: var(--foreground);
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.85em;
  border: 1px solid var(--border);
}

.reliable-hybrid-editor .overlay-code-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
  background-color: var(--muted);
  padding: 2px 2px;
  border-radius: 3px;
}

.reliable-hybrid-editor .overlay-link-text {
  color: var(--primary);
  text-decoration: underline;
}

.reliable-hybrid-editor .overlay-link-url {
  color: var(--muted-foreground);
  text-decoration: none;
}

.reliable-hybrid-editor .overlay-link-marker {
  color: var(--muted-foreground);
  opacity: 0.7;
}

.reliable-hybrid-editor .overlay-list-item {
  padding-left: 1.5em;
  margin: 0.2em 0;
  position: relative;
}

.reliable-hybrid-editor .overlay-numbered-list-item {
  padding-left: 1.5em;
  margin: 0.2em 0;
  position: relative;
}

.reliable-hybrid-editor .overlay-blockquote {
  border-left: 4px solid var(--primary);
  padding-left: 1em;
  margin: 0.5em 0;
  background-color: hsl(var(--muted) / 0.1);
  color: var(--muted-foreground);
  font-style: italic;
  padding: 0.5em 0 0.5em 1em;
}

.reliable-hybrid-editor .overlay-paragraph {
  margin: 0.5em 0;
  color: var(--foreground);
}

.reliable-hybrid-editor .overlay-paragraph:first-child {
  margin-top: 0;
}

.reliable-hybrid-editor .overlay-paragraph:last-child {
  margin-bottom: 0;
}