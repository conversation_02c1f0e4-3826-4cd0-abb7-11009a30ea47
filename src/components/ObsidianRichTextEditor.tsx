import { useState, useRef, useEffect, useCallback, forwardRef, useImperativeHandle } from 'react'
import { EditorView } from '@codemirror/view'
import { EditorState, Transaction } from '@codemirror/state'
import { richTextExtensions, createEditorState } from '../lib/codemirror-config'
import { Button } from './ui/button'
import { getDisplayName } from '../lib/utils'
import {
  Bold,
  Italic,
  Code,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Image,
  Minus,
  Save,
  Eye,
  Edit
} from 'lucide-react'

interface ObsidianRichTextEditorProps {
  content: string
  onChange: (content: string) => void
  onSave: () => void
  isDirty: boolean
  fileName?: string
  onFileRename?: (oldPath: string, newName: string) => void
  filePath?: string
}

export interface ObsidianRichTextEditorRef {
  formatBold: () => void
  formatItalic: () => void
  formatCode: () => void
  formatHeader: (level: number) => void
  formatList: (ordered?: boolean) => void
  insertLink: () => void
  insertHorizontalRule: () => void
  focus: () => void
}

export const ObsidianRichTextEditor = forwardRef<ObsidianRichTextEditorRef, ObsidianRichTextEditorProps>(({
  content,
  onChange,
  onSave,
  isDirty,
  fileName,
  onFileRename,
  filePath
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const valueRef = useRef(content)
  const [title, setTitle] = useState('')
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [showToolbar, setShowToolbar] = useState(true)
  const titleInputRef = useRef<HTMLInputElement>(null)

  // Extract title from filename
  useEffect(() => {
    if (fileName) {
      setTitle(getDisplayName(fileName))
    } else {
      setTitle('Untitled')
    }
  }, [fileName])

  // Update value ref when prop changes
  useEffect(() => {
    valueRef.current = content
  }, [content])

  // Handle document updates
  const handleUpdate = useCallback((transaction: Transaction) => {
    if (transaction.docChanged) {
      const newValue = transaction.state.doc.toString()
      if (newValue !== valueRef.current) {
        onChange(newValue)
      }
    }
  }, [onChange])

  // Formatting functions
  const formatBold = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `**${selectedText}**` : '**bold text**'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 2, head: from + 2 + (selectedText || 'bold text').length },
    })
    view.focus()
  }, [])

  const formatItalic = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `*${selectedText}*` : '*italic text*'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + (selectedText || 'italic text').length },
    })
    view.focus()
  }, [])

  const formatCode = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `\`${selectedText}\`` : '`code`'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + (selectedText || 'code').length },
    })
    view.focus()
  }, [])

  const formatHeader = useCallback((level: number) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const headerPrefix = '#'.repeat(level) + ' '

    // Remove existing header if present
    const cleanText = lineText.replace(/^#+\s*/, '')
    const newText = headerPrefix + (cleanText || `Header ${level}`)

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + headerPrefix.length },
    })
    view.focus()
  }, [])

  const formatList = useCallback((ordered = false) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const listPrefix = ordered ? '1. ' : '- '

    // Remove existing list markers if present
    const cleanText = lineText.replace(/^(\s*[-*+]|\s*\d+\.)\s*/, '')
    const newText = listPrefix + (cleanText || 'List item')

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + listPrefix.length },
    })
    view.focus()
  }, [])

  const formatQuote = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text

    // Remove existing quote if present
    const cleanText = lineText.replace(/^>\s*/, '')
    const newText = '> ' + (cleanText || 'Quote text')

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + 2 },
    })
    view.focus()
  }, [])

  const insertLink = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `[${selectedText}](url)` : '[link text](url)'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + newText.length - 4, head: from + newText.length - 1 },
    })
    view.focus()
  }, [])

  const insertImage = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `![${selectedText}](image-url)` : '![alt text](image-url)'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + newText.length - 11, head: from + newText.length - 1 },
    })
    view.focus()
  }, [])

  const insertHorizontalRule = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const newText = '\n---\n'

    view.dispatch({
      changes: { from: line.to, insert: newText },
      selection: { anchor: line.to + newText.length },
    })
    view.focus()
  }, [])

  const focus = useCallback(() => {
    viewRef.current?.focus()
  }, [])

  // Initialize CodeMirror with enhanced styling
  useEffect(() => {
    if (!editorRef.current) return

    try {
      const state = createEditorState(content, richTextExtensions)
      const view = new EditorView({
        state,
        parent: editorRef.current,
        dispatch: (transaction) => {
          view.update([transaction])
          handleUpdate(transaction)
        },
      })

      viewRef.current = view

      // Add keyboard shortcuts
      const handleKeyDown = (e: KeyboardEvent) => {
        const isCtrl = e.ctrlKey || e.metaKey

        if (isCtrl) {
          switch (e.key) {
            case 'b':
              e.preventDefault()
              formatBold()
              break
            case 'i':
              e.preventDefault()
              formatItalic()
              break
            case 'e':
              e.preventDefault()
              formatCode()
              break
            case 'k':
              e.preventDefault()
              insertLink()
              break
            case '1':
              e.preventDefault()
              formatHeader(1)
              break
            case '2':
              e.preventDefault()
              formatHeader(2)
              break
            case '3':
              e.preventDefault()
              formatHeader(3)
              break
            case 's':
              e.preventDefault()
              onSave()
              break
          }
        }
      }

      document.addEventListener('keydown', handleKeyDown)
      view.focus()

      return () => {
        view.destroy()
        viewRef.current = null
        document.removeEventListener('keydown', handleKeyDown)
      }
    } catch (error) {
      console.error('Error initializing ObsidianRichTextEditor:', error)
    }
  }, [formatBold, formatItalic, formatCode, formatHeader, insertLink, onSave, content, handleUpdate])

  // Update editor content when value prop changes externally
  useEffect(() => {
    const view = viewRef.current
    if (!view) return

    const currentValue = view.state.doc.toString()
    if (currentValue !== content) {
      view.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: content,
        },
      })
    }
  }, [content])

  // Title editing handlers
  const handleTitleEdit = () => {
    setIsEditingTitle(true)
  }

  const handleTitleSave = () => {
    setIsEditingTitle(false)
    if (onFileRename && filePath && title.trim() && title !== getDisplayName(fileName || '')) {
      onFileRename(filePath, title.trim() + '.md')
    }
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleTitleSave()
    } else if (e.key === 'Escape') {
      setTitle(getDisplayName(fileName || ''))
      setIsEditingTitle(false)
    }
  }

  // Focus title input when editing starts
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus()
      titleInputRef.current.select()
    }
  }, [isEditingTitle])

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    formatBold,
    formatItalic,
    formatCode,
    formatHeader,
    formatList,
    insertLink,
    insertHorizontalRule,
    focus,
  }), [formatBold, formatItalic, formatCode, formatHeader, formatList, insertLink, insertHorizontalRule, focus])

  return (
    <div className="obsidian-rich-editor h-full flex flex-col bg-background">
      {/* Obsidian-style Header */}
      <div className="obsidian-header border-b">
        <div className="max-w-[1000px] mx-auto px-8 py-6">
          <div className="flex items-start justify-between gap-6">
            {/* Title */}
            <div className="flex-1 min-w-0">
              {isEditingTitle ? (
                <input
                  ref={titleInputRef}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  onBlur={handleTitleSave}
                  onKeyDown={handleTitleKeyDown}
                  className="obsidian-title-input w-full text-left"
                  placeholder="Untitled"
                  autoFocus
                />
              ) : (
                <h1
                  className="obsidian-title cursor-pointer text-left"
                  onClick={handleTitleEdit}
                  title="Click to edit title"
                >
                  {title}
                </h1>
              )}
            </div>

            {/* Actions */}
            <div className="flex items-center gap-2 flex-shrink-0">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowToolbar(!showToolbar)}
                className="obsidian-action-button"
                title="Toggle toolbar"
              >
                {showToolbar ? <Eye className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={onSave}
                disabled={!isDirty}
                className="obsidian-action-button"
                title="Save (Ctrl+S)"
              >
                <Save className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Rich Text Toolbar */}
      {showToolbar && (
        <div className="obsidian-toolbar border-b bg-muted/30">
          <div className="max-w-[1000px] mx-auto px-8 py-3">
            <div className="flex items-center gap-1 flex-wrap">
              <Button variant="ghost" size="sm" onClick={formatBold} title="Bold (Ctrl+B)">
                <Bold className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={formatItalic} title="Italic (Ctrl+I)">
                <Italic className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={formatCode} title="Code (Ctrl+E)">
                <Code className="h-4 w-4" />
              </Button>

              <div className="w-px h-6 bg-border mx-2" />

              <Button variant="ghost" size="sm" onClick={() => formatHeader(1)} title="Header 1 (Ctrl+1)">
                <Heading1 className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => formatHeader(2)} title="Header 2 (Ctrl+2)">
                <Heading2 className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => formatHeader(3)} title="Header 3 (Ctrl+3)">
                <Heading3 className="h-4 w-4" />
              </Button>

              <div className="w-px h-6 bg-border mx-2" />

              <Button variant="ghost" size="sm" onClick={() => formatList(false)} title="Bullet List">
                <List className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={() => formatList(true)} title="Numbered List">
                <ListOrdered className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={formatQuote} title="Quote">
                <Quote className="h-4 w-4" />
              </Button>

              <div className="w-px h-6 bg-border mx-2" />

              <Button variant="ghost" size="sm" onClick={insertLink} title="Link (Ctrl+K)">
                <Link className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={insertImage} title="Image">
                <Image className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" onClick={insertHorizontalRule} title="Horizontal Rule">
                <Minus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Rich Text Content Area */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-[1000px] mx-auto px-8 py-8">
          <div
            ref={editorRef}
            className="obsidian-rich-content w-full min-h-[600px]"
            style={{ minHeight: '600px' }}
          />
        </div>
      </div>
    </div>
  )
})

ObsidianRichTextEditor.displayName = 'ObsidianRichTextEditor'
