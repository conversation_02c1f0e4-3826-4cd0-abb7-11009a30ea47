import { useState, useRef, useEffect, useCallback } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { wrapSelectedText, insertLinePrefix, insertTextAtCursor, getDisplayName } from '../lib/utils'
import {
  Bold,
  Italic,
  Code,
  List,
  ListOrdered,
  Quote,
  Heading1,
  Heading2,
  Heading3,
  Link,
  Image,
  Minus,
  Eye,
  Edit,
  Save
} from 'lucide-react'

interface RichMarkdownEditorProps {
  content: string
  onChange: (content: string) => void
  onSave: () => void
  isDirty: boolean
  fileName?: string
  onFileRename?: (oldPath: string, newName: string) => void
  filePath?: string
}

export function RichMarkdownEditor({ content, onChange, onSave, isDirty, fileName, onFileRename, filePath }: RichMarkdownEditorProps) {
  const [title, setTitle] = useState('')
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const [autoSaveTimeout, setAutoSaveTimeout] = useState<NodeJS.Timeout | null>(null)
  const editorRef = useRef<HTMLTextAreaElement>(null)
  const titleInputRef = useRef<HTMLInputElement>(null)

  // Extract title from filename (not content)
  useEffect(() => {
    if (fileName) {
      setTitle(getDisplayName(fileName))
    } else {
      setTitle('Untitled')
    }
  }, [fileName])

  // Auto-save functionality
  useEffect(() => {
    if (!isDirty) return

    // Clear existing timeout
    if (autoSaveTimeout) {
      clearTimeout(autoSaveTimeout)
    }

    // Set new timeout
    const timeout = setTimeout(() => {
      onSave()
    }, 2000) // Auto-save after 2 seconds of inactivity

    setAutoSaveTimeout(timeout)

    return () => {
      if (timeout) {
        clearTimeout(timeout)
      }
    }
  }, [isDirty]) // Only depend on isDirty, not content

  // Handle title editing and file renaming
  const handleTitleEdit = () => {
    setIsEditingTitle(true)
    setTimeout(() => titleInputRef.current?.focus(), 0)
  }

  const handleTitleSave = async () => {
    setIsEditingTitle(false)

    // If title changed and we have file rename capability, rename the file
    if (fileName && onFileRename && filePath && title !== getDisplayName(fileName)) {
      const extension = fileName.includes('.') ? '.' + fileName.split('.').pop() : '.md'
      const newFileName = title + extension
      try {
        await onFileRename(filePath, newFileName)
      } catch (error) {
        console.error('Error renaming file:', error)
        // Revert title on error
        setTitle(getDisplayName(fileName))
      }
    }
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleTitleSave()
    } else if (e.key === 'Escape') {
      setIsEditingTitle(false)
      // Revert title
      if (fileName) {
        setTitle(getDisplayName(fileName))
      }
    }
  }

  // Handle content changes
  const handleContentChange = (newContent: string) => {
    onChange(newContent)
  }

  // Handle keyboard shortcuts
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!editorRef.current) return

    const isCtrl = e.ctrlKey || e.metaKey
    const textarea = editorRef.current

    // Save shortcut
    if (isCtrl && e.key === 's') {
      e.preventDefault()
      onSave()
      return
    }

    // Basic formatting shortcuts
    if (isCtrl) {
      switch (e.key) {
        case 'b': // Bold
          e.preventDefault()
          handleContentChange(wrapSelectedText(textarea, '**'))
          break
        case 'i': // Italic
          e.preventDefault()
          handleContentChange(wrapSelectedText(textarea, '*'))
          break
        case '`': // Code
          e.preventDefault()
          handleContentChange(wrapSelectedText(textarea, '`'))
          break
      }
    }
  }

  // No formatting functions needed - users type markdown directly

  return (
    <div className="obsidian-editor h-full flex flex-col bg-background">
      {/* Obsidian-style Header */}
      <div className="obsidian-header">
        <div className="max-w-[1000px] mx-auto px-8 py-6">
          <div className="flex items-start justify-between gap-6">
            {/* Title - Left aligned */}
            <div className="flex-1 min-w-0">
              {isEditingTitle ? (
                <input
                  ref={titleInputRef}
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  onBlur={handleTitleSave}
                  onKeyDown={handleTitleKeyDown}
                  className="obsidian-title-input w-full text-left"
                  placeholder="Untitled"
                  autoFocus
                />
              ) : (
                <h1
                  className="obsidian-title cursor-pointer text-left"
                  onClick={handleTitleEdit}
                  title="Click to edit title"
                >
                  {title}
                </h1>
              )}
            </div>

            {/* Auto-save indicator - Right aligned */}
            <div className="auto-save-indicator flex items-center gap-2 flex-shrink-0 ml-auto">
              {isDirty ? (
                <span className="saving flex items-center gap-2">
                  <div className="w-2 h-2 bg-orange-500 rounded-full pulse-dot" />
                  <span className="text-sm font-medium">Auto-saving...</span>
                </span>
              ) : (
                <span className="saved flex items-center gap-2">
                  <Save className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-600">Saved</span>
                </span>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Content Area - Clean Markdown Editor */}
      <div className="flex-1 overflow-auto">
        <div className="max-w-[1000px] mx-auto px-8 py-8">
          <textarea
            ref={editorRef}
            value={content}
            onChange={(e) => handleContentChange(e.target.value)}
            onKeyDown={handleKeyDown}
            className="obsidian-unified-editor w-full min-h-[600px] resize-none border-none outline-none bg-transparent"
            placeholder="Start writing your thoughts..."
            spellCheck={false}
            autoFocus={true}
          />
        </div>
      </div>
    </div>
  )
}
