import { useRef, useCallback, forwardRef, useImperativeHandle, useEffect } from 'react'

interface ReliableHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface ReliableHybridEditorRef {
  focus: () => void
}

export const ReliableHybridEditor = forwardRef<ReliableHybridEditorRef, ReliableHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)

  // Convert markdown text to styled HTML for overlay
  const renderOverlayContent = useCallback((text: string) => {
    if (!text) return ''

    const lines = text.split('\n')
    
    return lines.map((line, index) => {
      let processedLine = line

      // Headers - make them large but keep # visible
      if (line.startsWith('# ')) {
        return `<div class="overlay-h1">${line}</div>`
      } else if (line.startsWith('## ')) {
        return `<div class="overlay-h2">${line}</div>`
      } else if (line.startsWith('### ')) {
        return `<div class="overlay-h3">${line}</div>`
      } else if (line.startsWith('#### ')) {
        return `<div class="overlay-h4">${line}</div>`
      } else if (line.startsWith('##### ')) {
        return `<div class="overlay-h5">${line}</div>`
      } else if (line.startsWith('###### ')) {
        return `<div class="overlay-h6">${line}</div>`
      } else {
        // Process inline formatting
        
        // Bold text - make content bold but keep ** visible
        processedLine = processedLine.replace(/\*\*([^*\n]+)\*\*/g, (match, content) => {
          return `<span class="overlay-bold-marker">**</span><span class="overlay-bold-content">${content}</span><span class="overlay-bold-marker">**</span>`
        })

        // Italic text - make content italic but keep * visible
        processedLine = processedLine.replace(/(?<!\*)\*([^*\n]+)\*(?!\*)/g, (match, content) => {
          return `<span class="overlay-italic-marker">*</span><span class="overlay-italic-content">${content}</span><span class="overlay-italic-marker">*</span>`
        })

        // Code - add background but keep ` visible
        processedLine = processedLine.replace(/`([^`\n]+)`/g, (match, content) => {
          return `<span class="overlay-code-marker">\`</span><span class="overlay-code-content">${content}</span><span class="overlay-code-marker">\`</span>`
        })

        // Links
        processedLine = processedLine.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
          return `<span class="overlay-link-marker">[</span><span class="overlay-link-text">${text}</span><span class="overlay-link-marker">](</span><span class="overlay-link-url">${url}</span><span class="overlay-link-marker">)</span>`
        })

        // Blockquotes
        if (line.startsWith('> ')) {
          return `<div class="overlay-blockquote">${processedLine}</div>`
        }

        // List items
        if (line.match(/^[\s]*[-*+]\s/)) {
          return `<div class="overlay-list-item">${processedLine}</div>`
        }

        if (line.match(/^[\s]*\d+\.\s/)) {
          return `<div class="overlay-numbered-list-item">${processedLine}</div>`
        }

        return `<div class="overlay-paragraph">${processedLine || '<br>'}</div>`
      }
    }).join('')
  }, [])

  // Update overlay when content changes
  useEffect(() => {
    if (overlayRef.current) {
      const styledContent = renderOverlayContent(value)
      overlayRef.current.innerHTML = styledContent
    }
  }, [value, renderOverlayContent])

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value)
  }

  // Sync scroll between textarea and overlay
  const handleScroll = () => {
    if (textareaRef.current && overlayRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft
    }
  }

  const focus = useCallback(() => {
    textareaRef.current?.focus()
  }, [])

  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  return (
    <div className={`reliable-hybrid-editor ${className}`}>
      <div className="hybrid-container">
        {/* Styled overlay - shows behind textarea */}
        <div
          ref={overlayRef}
          className="hybrid-overlay"
          onScroll={handleScroll}
        />
        
        {/* Transparent textarea on top */}
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={onKeyDown}
          onScroll={handleScroll}
          className="hybrid-textarea"
          placeholder={placeholder}
          spellCheck={false}
          autoFocus={autoFocus}
        />
      </div>
    </div>
  )
})
