import { useRef, useCallback, forwardRef, useImperativeHandle, useEffect } from 'react'

interface BasicHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface BasicHybridEditorRef {
  focus: () => void
}

export const BasicHybridEditor = forwardRef<BasicHybridEditorRef, BasicHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value)
  }

  // Apply basic hybrid styling by adding CSS classes based on content
  useEffect(() => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const lines = value.split('\n')
    
    // Remove existing hybrid classes
    textarea.classList.remove('has-h1', 'has-h2', 'has-h3', 'has-bold', 'has-italic', 'has-code')
    
    // Check for markdown patterns and add classes
    let hasH1 = false, hasH2 = false, hasH3 = false
    let hasBold = false, hasItalic = false, hasCode = false
    
    lines.forEach(line => {
      if (line.startsWith('# ')) hasH1 = true
      if (line.startsWith('## ')) hasH2 = true
      if (line.startsWith('### ')) hasH3 = true
      if (line.includes('**')) hasBold = true
      if (line.match(/(?<!\*)\*[^*]+\*(?!\*)/)) hasItalic = true
      if (line.includes('`')) hasCode = true
    })
    
    // Add classes based on content
    if (hasH1) textarea.classList.add('has-h1')
    if (hasH2) textarea.classList.add('has-h2')
    if (hasH3) textarea.classList.add('has-h3')
    if (hasBold) textarea.classList.add('has-bold')
    if (hasItalic) textarea.classList.add('has-italic')
    if (hasCode) textarea.classList.add('has-code')
  }, [value])

  const focus = useCallback(() => {
    textareaRef.current?.focus()
  }, [])

  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  return (
    <textarea
      ref={textareaRef}
      value={value}
      onChange={handleChange}
      onKeyDown={onKeyDown}
      className={`basic-hybrid-editor ${className}`}
      placeholder={placeholder}
      spellCheck={false}
      autoFocus={autoFocus}
    />
  )
})
