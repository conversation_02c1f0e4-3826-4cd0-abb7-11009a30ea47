import { useEffect, useRef, useCallback, forwardRef, useImperativeHandle, useState } from 'react'
import { EditorView } from '@codemirror/view'
import { EditorState, Transaction } from '@codemirror/state'
import { basicExtensions, createEditorState } from '../lib/codemirror-config'
import { getDisplayName } from '../lib/utils'

interface ObsidianStyleEditorProps {
  content: string
  onChange: (content: string) => void
  onSave: () => void
  isDirty: boolean
  fileName?: string
  onFileRename?: (oldPath: string, newName: string) => void
  filePath?: string
  autoFocus?: boolean
  placeholder?: string
}

export interface ObsidianStyleEditorRef {
  formatBold: () => void
  formatItalic: () => void
  formatCode: () => void
  formatHeader: (level: number) => void
  formatList: (ordered?: boolean) => void
  insertLink: () => void
  insertHorizontalRule: () => void
  focus: () => void
}

export const ObsidianStyleEditor = forwardRef<ObsidianStyleEditorRef, ObsidianStyleEditorProps>(({
  content,
  onChange,
  onSave,
  isDirty,
  fileName,
  onFileRename,
  filePath,
  autoFocus = false,
  placeholder = 'Start writing your thoughts...'
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const valueRef = useRef(content)
  const [title, setTitle] = useState('')
  const [isEditingTitle, setIsEditingTitle] = useState(false)
  const titleInputRef = useRef<HTMLInputElement>(null)

  // Extract title from filename
  useEffect(() => {
    if (fileName) {
      setTitle(getDisplayName(fileName))
    } else {
      setTitle('Untitled')
    }
  }, [fileName])

  // Update value ref when prop changes
  useEffect(() => {
    valueRef.current = content
  }, [content])

  // Handle document updates
  const handleUpdate = useCallback((transaction: Transaction) => {
    if (transaction.docChanged) {
      const newValue = transaction.state.doc.toString()
      if (newValue !== valueRef.current) {
        onChange(newValue)
      }
    }
  }, [onChange])

  // Formatting functions (defined before useEffect)
  const formatBold = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `**${selectedText}**`

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 2, head: from + 2 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatItalic = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `*${selectedText}*`

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatCode = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `\`${selectedText}\``

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatHeader = useCallback((level: number) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const headerPrefix = '#'.repeat(level) + ' '

    // Remove existing header if present
    const cleanText = lineText.replace(/^#+\s*/, '')
    const newText = headerPrefix + cleanText

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + headerPrefix.length },
    })
    view.focus()
  }, [])

  const formatList = useCallback((ordered = false) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const listPrefix = ordered ? '1. ' : '- '

    // Remove existing list markers if present
    const cleanText = lineText.replace(/^(\s*[-*+]|\s*\d+\.)\s*/, '')
    const newText = listPrefix + cleanText

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + listPrefix.length },
    })
    view.focus()
  }, [])

  const insertLink = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = selectedText ? `[${selectedText}](url)` : '[text](url)'

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + newText.length - 4, head: from + newText.length - 1 },
    })
    view.focus()
  }, [])

  const insertHorizontalRule = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const newText = '\n---\n'

    view.dispatch({
      changes: { from: line.to, insert: newText },
      selection: { anchor: line.to + newText.length },
    })
    view.focus()
  }, [])

  const focus = useCallback(() => {
    viewRef.current?.focus()
  }, [])

  // Initialize CodeMirror with Obsidian styling
  useEffect(() => {
    if (!editorRef.current) {
      console.log('ObsidianStyleEditor: editorRef.current is null')
      return
    }

    try {
      console.log('ObsidianStyleEditor: Initializing CodeMirror with content:', content.substring(0, 100))
      const state = createEditorState(content, basicExtensions)
      const view = new EditorView({
        state,
        parent: editorRef.current,
        dispatch: (transaction) => {
          view.update([transaction])
          handleUpdate(transaction)
        },
      })

      viewRef.current = view
      console.log('ObsidianStyleEditor: CodeMirror initialized successfully')
    } catch (error) {
      console.error('ObsidianStyleEditor: Error initializing CodeMirror:', error)
      return
    }

    // Add global keyboard shortcuts
    const handleKeyDown = (e: KeyboardEvent) => {
      const isCtrl = e.ctrlKey || e.metaKey

      if (isCtrl) {
        switch (e.key) {
          case 'b':
            e.preventDefault()
            formatBold()
            break
          case 'i':
            e.preventDefault()
            formatItalic()
            break
          case 'e':
            e.preventDefault()
            formatCode()
            break
          case 'k':
            e.preventDefault()
            insertLink()
            break
          case '1':
            e.preventDefault()
            formatHeader(1)
            break
          case '2':
            e.preventDefault()
            formatHeader(2)
            break
          case '3':
            e.preventDefault()
            formatHeader(3)
            break
          case 's':
            e.preventDefault()
            onSave()
            break
        }
      }
    }

    // Add event listener for keyboard shortcuts
    document.addEventListener('keydown', handleKeyDown)

    // Auto focus if requested
    if (autoFocus) {
      view.focus()
    }

    return () => {
      view.destroy()
      viewRef.current = null
      document.removeEventListener('keydown', handleKeyDown)
    }
  }, [formatBold, formatItalic, formatCode, formatHeader, formatList, insertLink, onSave, autoFocus, content, handleUpdate]) // Add dependencies

  // Update editor content when value prop changes externally
  useEffect(() => {
    const view = viewRef.current
    if (!view) return

    const currentValue = view.state.doc.toString()
    if (currentValue !== content) {
      view.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: content,
        },
      })
    }
  }, [content])

  // Title editing handlers
  const handleTitleEdit = () => {
    setIsEditingTitle(true)
  }

  const handleTitleSave = () => {
    setIsEditingTitle(false)
    if (onFileRename && filePath && title.trim() && title !== getDisplayName(fileName || '')) {
      onFileRename(filePath, title.trim() + '.md')
    }
  }

  const handleTitleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleTitleSave()
    } else if (e.key === 'Escape') {
      setTitle(getDisplayName(fileName || ''))
      setIsEditingTitle(false)
    }
  }

  // Focus title input when editing starts
  useEffect(() => {
    if (isEditingTitle && titleInputRef.current) {
      titleInputRef.current.focus()
      titleInputRef.current.select()
    }
  }, [isEditingTitle])



  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    formatBold,
    formatItalic,
    formatCode,
    formatHeader,
    formatList,
    insertLink,
    insertHorizontalRule,
    focus,
  }), [formatBold, formatItalic, formatCode, formatHeader, formatList, insertLink, insertHorizontalRule, focus])

  return (
    <div
      className="markdown-source-view cm-s-obsidian mod-cm6 node-insert-event is-readable-line-width is-live-preview is-folding show-properties"
      style={{
        height: '100%',
        width: '100%',
        backgroundColor: 'var(--background)',
        color: 'var(--foreground)',
        padding: '20px'
      }}
    >
      {/* Inline title matching Obsidian structure */}
      <div className="cm-editor cm-focused ͼ1 ͼ2" style={{ height: '100%' }}>
        <div className="cm-announced" aria-live="polite"></div>
        <div tabIndex={-1} className="cm-scroller" style={{ height: '100%' }}>
          <div className="cm-sizer" style={{ maxWidth: '700px', margin: '0 auto', width: '100%' }}>
            {/* Inline title */}
            {isEditingTitle ? (
              <input
                ref={titleInputRef}
                value={title}
                onChange={(e) => setTitle(e.target.value)}
                onBlur={handleTitleSave}
                onKeyDown={handleTitleKeyDown}
                className="inline-title"
                spellCheck={true}
                autoCapitalize="on"
                tabIndex={-1}
                placeholder="Untitled"
                autoFocus
                style={{
                  fontSize: '2.25rem',
                  fontWeight: '700',
                  lineHeight: '1.2',
                  color: 'var(--foreground)',
                  margin: '0 0 1rem 0',
                  padding: '0',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                  width: '100%',
                  fontFamily: 'inherit'
                }}
              />
            ) : (
              <div
                className="inline-title"
                spellCheck={true}
                autoCapitalize="on"
                tabIndex={-1}
                onClick={handleTitleEdit}
                style={{
                  cursor: 'pointer',
                  fontSize: '2.25rem',
                  fontWeight: '700',
                  lineHeight: '1.2',
                  color: 'var(--foreground)',
                  margin: '0 0 1rem 0',
                  padding: '0',
                  background: 'transparent',
                  border: 'none',
                  outline: 'none',
                  width: '100%',
                  fontFamily: 'inherit',
                  minHeight: '1.2em'
                }}
              >
                {title || 'Untitled'}
              </div>
            )}

            {/* CodeMirror content container */}
            <div className="cm-contentContainer" style={{ width: '100%', minHeight: '600px' }}>
              <div
                ref={editorRef}
                className="obsidian-codemirror-wrapper"
                style={{
                  minHeight: '600px',
                  width: '100%',
                  backgroundColor: 'transparent',
                  border: 'none'
                }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
})

ObsidianStyleEditor.displayName = 'ObsidianStyleEditor'
