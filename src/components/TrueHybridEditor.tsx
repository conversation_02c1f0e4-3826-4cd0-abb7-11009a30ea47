import { useRef, useCallback, forwardRef, useImperative<PERSON><PERSON><PERSON>, useEffect, useState } from 'react'

interface TrueHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: React.KeyboardEvent<HTMLDivElement>) => void
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface TrueHybridEditorRef {
  focus: () => void
}

export const TrueHybridEditor = forwardRef<TrueHybridEditorRef, TrueHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const [isComposing, setIsComposing] = useState(false)

  // Convert markdown text to styled HTML while keeping syntax visible
  const renderHybridContent = useCallback((text: string) => {
    if (!text) return ''

    const lines = text.split('\n')

    return lines.map((line, index) => {
      let processedLine = line

      // Headers - make them large but keep # visible
      if (line.startsWith('# ')) {
        return `<div class="hybrid-h1" data-line="${index}">${line}</div>`
      } else if (line.startsWith('## ')) {
        return `<div class="hybrid-h2" data-line="${index}">${line}</div>`
      } else if (line.startsWith('### ')) {
        return `<div class="hybrid-h3" data-line="${index}">${line}</div>`
      } else if (line.startsWith('#### ')) {
        return `<div class="hybrid-h4" data-line="${index}">${line}</div>`
      } else if (line.startsWith('##### ')) {
        return `<div class="hybrid-h5" data-line="${index}">${line}</div>`
      } else if (line.startsWith('###### ')) {
        return `<div class="hybrid-h6" data-line="${index}">${line}</div>`
      } else {
        // Process inline formatting

        // Bold text - make content bold but keep ** visible
        processedLine = processedLine.replace(/\*\*([^*\n]+)\*\*/g, (match, content) => {
          return `<span class="hybrid-bold-marker">**</span><span class="hybrid-bold-content">${content}</span><span class="hybrid-bold-marker">**</span>`
        })

        // Italic text - make content italic but keep * visible
        processedLine = processedLine.replace(/(?<!\*)\*([^*\n]+)\*(?!\*)/g, (match, content) => {
          return `<span class="hybrid-italic-marker">*</span><span class="hybrid-italic-content">${content}</span><span class="hybrid-italic-marker">*</span>`
        })

        // Code - add background but keep ` visible
        processedLine = processedLine.replace(/`([^`\n]+)`/g, (match, content) => {
          return `<span class="hybrid-code-marker">\`</span><span class="hybrid-code-content">${content}</span><span class="hybrid-code-marker">\`</span>`
        })

        // Links
        processedLine = processedLine.replace(/\[([^\]]+)\]\(([^)]+)\)/g, (match, text, url) => {
          return `<span class="hybrid-link-marker">[</span><span class="hybrid-link-text">${text}</span><span class="hybrid-link-marker">](</span><span class="hybrid-link-url">${url}</span><span class="hybrid-link-marker">)</span>`
        })

        // Blockquotes
        if (line.startsWith('> ')) {
          return `<div class="hybrid-blockquote" data-line="${index}">${processedLine}</div>`
        }

        // List items
        if (line.match(/^[\s]*[-*+]\s/)) {
          return `<div class="hybrid-list-item" data-line="${index}">${processedLine}</div>`
        }

        if (line.match(/^[\s]*\d+\.\s/)) {
          return `<div class="hybrid-numbered-list-item" data-line="${index}">${processedLine}</div>`
        }

        return `<div class="hybrid-paragraph" data-line="${index}">${processedLine || '<br>'}</div>`
      }
    }).join('')
  }, [])

  // Update content when value changes (only from external changes)
  useEffect(() => {
    if (!editorRef.current || isComposing) return

    const currentText = editorRef.current.innerText || ''
    if (currentText !== value) {
      // Save cursor position
      const selection = window.getSelection()
      const range = selection?.rangeCount ? selection.getRangeAt(0) : null
      const cursorOffset = range ? range.startOffset : 0

      const styledContent = renderHybridContent(value)
      editorRef.current.innerHTML = styledContent

      // Try to restore cursor position
      if (range && editorRef.current.firstChild) {
        try {
          const newRange = document.createRange()
          const textNode = editorRef.current.firstChild
          const maxOffset = textNode.textContent?.length || 0
          newRange.setStart(textNode, Math.min(cursorOffset, maxOffset))
          newRange.collapse(true)
          selection?.removeAllRanges()
          selection?.addRange(newRange)
        } catch (e) {
          // Fallback: place cursor at end
          const newRange = document.createRange()
          newRange.selectNodeContents(editorRef.current)
          newRange.collapse(false)
          selection?.removeAllRanges()
          selection?.addRange(newRange)
        }
      }
    }
  }, [value, renderHybridContent, isComposing])

  // Handle input changes with debouncing
  const handleInput = useCallback(() => {
    if (!editorRef.current || isComposing) return

    const text = editorRef.current.innerText || ''
    onChange(text)
  }, [onChange, isComposing])

  // Handle composition events (for IME input)
  const handleCompositionStart = () => {
    setIsComposing(true)
  }

  const handleCompositionEnd = () => {
    setIsComposing(false)
    handleInput()
  }

  const focus = useCallback(() => {
    editorRef.current?.focus()
  }, [])

  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  // Auto focus
  useEffect(() => {
    if (autoFocus && editorRef.current) {
      editorRef.current.focus()
    }
  }, [autoFocus])

  return (
    <div
      ref={editorRef}
      contentEditable
      suppressContentEditableWarning
      onInput={handleInput}
      onCompositionStart={handleCompositionStart}
      onCompositionEnd={handleCompositionEnd}
      onKeyDown={onKeyDown}
      className={`true-hybrid-editor ${className}`}
      data-placeholder={placeholder}
      style={{ minHeight: '600px' }}
      dangerouslySetInnerHTML={{ __html: renderHybridContent(value) }}
    />
  )
})
