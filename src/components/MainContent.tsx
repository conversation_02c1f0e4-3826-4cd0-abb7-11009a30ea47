import { useState, useEffect, useRef } from 'react'
import { But<PERSON> } from './ui/button'
import { Textarea } from './ui/textarea'
import { isMarkdownFile, isCsvFile, wrapSelectedText, insertLinePrefix, insertTextAtCursor } from '../lib/utils'
import { FileText, Database, Edit, Eye } from 'lucide-react'
import { DatabaseTable } from './DatabaseTable'
import { MarkdownToolbar } from './MarkdownToolbar'
import { RichMarkdownEditor } from './RichMarkdownEditor'

interface MainContentProps {
  selectedFile: string | null
  onFileChange: () => void
  onFileRename?: (oldPath: string, newName: string) => void
}

export function MainContent({ selectedFile, onFileChange, onFileRename }: MainContentProps) {
  const [fileContent, setFileContent] = useState('')
  const [isEditing, setIsEditing] = useState(true)
  const [isDirty, setIsDirty] = useState(false)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    if (selectedFile) {
      loadFile(selectedFile)
    } else {
      setFileContent('')
      setIsDirty(false)
    }
  }, [selectedFile])

  const loadFile = async (filePath: string) => {
    try {
      const content = await window.electronAPI.readFile(filePath)
      setFileContent(content)
      setIsDirty(false)
    } catch (error) {
      console.error('Error loading file:', error)
      setFileContent('')
    }
  }

  const saveFile = async () => {
    if (!selectedFile || !isDirty) return

    try {
      await window.electronAPI.writeFile(selectedFile, fileContent)
      setIsDirty(false)
      onFileChange()
    } catch (error) {
      console.error('Error saving file:', error)
    }
  }

  const handleContentChange = (newContent: string) => {
    setFileContent(newContent)
    setIsDirty(true)
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (!textareaRef.current) return

    const isCtrl = e.ctrlKey || e.metaKey

    if (isCtrl) {
      switch (e.key) {
        case 'b':
          e.preventDefault()
          handleContentChange(wrapSelectedText(textareaRef.current, '**'))
          break
        case 'i':
          e.preventDefault()
          handleContentChange(wrapSelectedText(textareaRef.current, '*'))
          break
        case '`':
          e.preventDefault()
          handleContentChange(wrapSelectedText(textareaRef.current, '`'))
          break
        case 'k':
          e.preventDefault()
          handleContentChange(insertTextAtCursor(textareaRef.current, '[Link Text](https://example.com)'))
          break
        case '1':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '# '))
          break
        case '2':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '## '))
          break
        case '3':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '### '))
          break
        case 's':
          e.preventDefault()
          saveFile()
          break
      }
    }

    if (isCtrl && e.shiftKey) {
      switch (e.key) {
        case '*':
        case '8':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '- '))
          break
        case '&':
        case '7':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '1. '))
          break
        case '>':
        case '.':
          e.preventDefault()
          handleContentChange(insertLinePrefix(textareaRef.current, '> '))
          break
      }
    }

    // Auto-formatting shortcuts
    if (e.key === 'Enter') {
      const textarea = textareaRef.current
      const cursorPos = textarea.selectionStart
      const textBeforeCursor = textarea.value.substring(0, cursorPos)
      const currentLine = textBeforeCursor.split('\n').pop() || ''

      // Auto-continue lists
      const listMatch = currentLine.match(/^(\s*)([-*+]|\d+\.)\s/)
      if (listMatch) {
        e.preventDefault()
        const indent = listMatch[1]
        const marker = listMatch[2]

        if (currentLine.trim() === marker) {
          // Empty list item, remove it
          const newContent = textarea.value.substring(0, cursorPos - currentLine.length) +
                           textarea.value.substring(cursorPos)
          handleContentChange(newContent)
          setTimeout(() => {
            textarea.selectionStart = textarea.selectionEnd = cursorPos - currentLine.length
          }, 0)
        } else {
          // Continue list
          const nextMarker = marker.match(/\d+/) ?
            `${parseInt(marker) + 1}.` : marker
          const newLine = `\n${indent}${nextMarker} `
          handleContentChange(insertTextAtCursor(textarea, newLine))
        }
      }
    }
  }

  const renderMarkdownEditor = () => (
    <div className="main-content h-full">
      <RichMarkdownEditor
        content={fileContent}
        onChange={handleContentChange}
        onSave={saveFile}
        isDirty={isDirty}
        fileName={selectedFile ? selectedFile.split('/').pop() : undefined}
        onFileRename={onFileRename}
        filePath={selectedFile || undefined}
      />
    </div>
  )

  const renderDatabaseEditor = () => (
    <div className="main-content h-full">
      <DatabaseTable
        content={fileContent}
        onContentChange={handleContentChange}
        isDirty={isDirty}
        onSave={saveFile}
      />
    </div>
  )

  const renderWelcome = () => (
    <div className="main-content h-full flex items-center justify-center">
      <div className="text-center text-muted-foreground">
        <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-lg flex items-center justify-center">
          <FileText className="w-8 h-8" />
        </div>
        <h3 className="text-lg font-medium mb-2">
          No page selected
        </h3>
        <p>
          Select a page from the sidebar to start editing
        </p>
      </div>
    </div>
  )

  if (!selectedFile) {
    return renderWelcome()
  }

  if (isMarkdownFile(selectedFile)) {
    return renderMarkdownEditor()
  }

  if (isCsvFile(selectedFile)) {
    return renderDatabaseEditor()
  }

  return (
    <div className="main-content h-full flex items-center justify-center">
      <div className="text-center text-muted-foreground">
        <p>Unsupported file type</p>
      </div>
    </div>
  )
}
