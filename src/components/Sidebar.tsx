
import { useState } from 'react'
import { Vault, FileSystemItem } from '../types'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from './ui/dialog'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from './ui/dropdown-menu'
import { ChevronDown, FileText, Database, Plus, Folder, FolderOpen, FolderPlus } from 'lucide-react'
import { getDisplayName, generateId } from '../lib/utils'

interface SidebarProps {
  vault: Vault
  vaults: Vault[]
  fileTree: FileSystemItem[]
  selectedFile: string | null
  onFileSelect: (filePath: string) => void
  onFileCreate: (name: string, isDirectory: boolean, parentPath?: string) => void
  onFileDelete: (filePath: string, isDirectory: boolean) => void
  onFileMove: (sourcePath: string, targetFolderPath: string) => void
  onVaultClose: () => void
  onVaultSwitch: (vault: Vault) => void
  onVaultCreate?: (vault: Vault) => void
}

export function Sidebar({
  vault,
  vaults,
  fileTree,
  selectedFile,
  onFileSelect,
  onFileCreate,
  onFileDelete: _onFileDelete,
  onFileMove,
  onVaultClose,
  onVaultSwitch,
  onVaultCreate
}: SidebarProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newFileName, setNewFileName] = useState('')
  const [createType, setCreateType] = useState<'markdown' | 'database' | 'folder'>('markdown')
  const [draggedItem, setDraggedItem] = useState<FileSystemItem | null>(null)
  const [showVaultCreateDialog, setShowVaultCreateDialog] = useState(false)
  const [newVaultName, setNewVaultName] = useState('')
  const [isCreatingVault, setIsCreatingVault] = useState(false)
  const [selectedParentFolder, setSelectedParentFolder] = useState<string | null>(null)

  const handleCreateFile = async () => {
    if (!newFileName.trim()) return

    try {
      const parentPath = selectedParentFolder || vault.path

      if (createType === 'folder') {
        // Create folder
        const fullPath = `${parentPath}/${newFileName}`
        await window.electronAPI.createDirectory(fullPath)
        onFileCreate(newFileName, true, parentPath)
      } else {
        // Create file
        const extension = createType === 'markdown' ? '.md' : '.csv'
        const fileName = newFileName.endsWith(extension) ? newFileName : `${newFileName}${extension}`
        const fullPath = `${parentPath}/${fileName}`

        if (createType === 'markdown') {
          const defaultContent = `# ${newFileName}\n\nStart writing your page here...`
          await window.electronAPI.writeFile(fullPath, defaultContent)
        } else {
          const defaultContent = `Name,Type,Status\nExample Item,Task,Todo\n`
          await window.electronAPI.writeFile(fullPath, defaultContent)
        }

        onFileCreate(fileName, false, parentPath)
      }

      setShowCreateDialog(false)
      setNewFileName('')
      setSelectedParentFolder(null)
    } catch (error) {
      console.error('Error creating file/folder:', error)
    }
  }

  const openCreateDialog = () => {
    setShowCreateDialog(true)
  }

  const handleOpenVault = async () => {
    try {
      const selectedPath = await window.electronAPI.selectDirectory()
      if (selectedPath) {
        const vaultName = selectedPath.split('/').pop() || 'Unnamed Vault'
        const vault: Vault = {
          id: generateId(),
          name: vaultName,
          path: selectedPath,
          lastOpened: new Date()
        }
        onVaultSwitch(vault)
        if (onVaultCreate) {
          onVaultCreate(vault)
        }
      }
    } catch (error) {
      console.error('Error opening vault:', error)
    }
  }

  const handleCreateVault = async () => {
    if (!newVaultName.trim()) return

    setIsCreatingVault(true)
    try {
      const selectedPath = await window.electronAPI.selectDirectory()
      if (selectedPath) {
        const vaultPath = `${selectedPath}/${newVaultName}`

        // Create the vault directory
        await window.electronAPI.createDirectory(vaultPath)

        // Create a welcome page
        const welcomeContent = `# Welcome to ${newVaultName}

This is your new KaptureHQ vault!

## Getting Started

- Create pages using the "Add page" button in the sidebar
- Create databases and folders to organize your content
- All your pages are stored as files in this vault folder

Happy organizing! 📝`

        await window.electronAPI.writeFile(`${vaultPath}/Welcome.md`, welcomeContent)

        const vault: Vault = {
          id: generateId(),
          name: newVaultName,
          path: vaultPath,
          lastOpened: new Date()
        }

        onVaultSwitch(vault)
        if (onVaultCreate) {
          onVaultCreate(vault)
        }
        setShowVaultCreateDialog(false)
        setNewVaultName('')
      }
    } catch (error) {
      console.error('Error creating vault:', error)
    } finally {
      setIsCreatingVault(false)
    }
  }

  const getFileIcon = (item: FileSystemItem) => {
    if (item.isDirectory) {
      return <Folder className="tree-item-icon" />
    }
    return item.name.endsWith('.csv') ?
      <Database className="tree-item-icon" /> :
      <FileText className="tree-item-icon" />
  }

  const handleDragStart = (e: React.DragEvent, item: FileSystemItem) => {
    setDraggedItem(item)
    e.dataTransfer.effectAllowed = 'move'
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    e.dataTransfer.dropEffect = 'move'
  }

  const handleDrop = (e: React.DragEvent, targetItem: FileSystemItem) => {
    e.preventDefault()
    if (!draggedItem || !targetItem.isDirectory) return

    // Prevent dropping a folder into itself or its children
    if (draggedItem.isDirectory && targetItem.path.startsWith(draggedItem.path)) {
      setDraggedItem(null)
      return
    }

    onFileMove(draggedItem.path, targetItem.path)
    setDraggedItem(null)
  }

  const handleFolderRightClick = (e: React.MouseEvent, item: FileSystemItem) => {
    if (!item.isDirectory) return
    e.preventDefault()
    setSelectedParentFolder(item.path)
    setShowCreateDialog(true)
  }

  const renderFileTree = (items: FileSystemItem[], level = 0) => {
    return items.map((item) => (
      <div key={item.path} style={{ marginLeft: `${level * 16}px` }}>
        <div
          className={`tree-item ${selectedFile === item.path ? 'selected' : ''} ${
            item.isDirectory ? 'drop-target' : ''
          }`}
          onClick={() => !item.isDirectory && onFileSelect(item.path)}
          onContextMenu={(e) => handleFolderRightClick(e, item)}
          draggable={true}
          onDragStart={(e) => handleDragStart(e, item)}
          onDragOver={item.isDirectory ? handleDragOver : undefined}
          onDrop={item.isDirectory ? (e) => handleDrop(e, item) : undefined}
        >
          {getFileIcon(item)}
          <span className="tree-item-text">
            {item.isDirectory ? item.name : getDisplayName(item.name)}
          </span>
        </div>
        {item.isDirectory && item.children && (
          <div>
            {renderFileTree(item.children, level + 1)}
          </div>
        )}
      </div>
    ))
  }

  // Filter out the old Notes and Databases folders, show all files in root
  const filteredTree = fileTree.filter(item => {
    // Skip the old Notes and Databases folders if they exist
    if (item.isDirectory && (item.name === 'Notes' || item.name === 'Databases')) {
      return false
    }
    return true
  })

  return (
    <div className="sidebar flex flex-col h-full">
      {/* Header - Vault Switcher */}
      <div className="p-4 border-b border-border/50">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="w-full justify-between h-auto p-2">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-md flex items-center justify-center text-white text-xs font-semibold">
                  {vault.name.charAt(0).toUpperCase()}
                </div>
                <span className="font-medium truncate">{vault.name}</span>
              </div>
              <ChevronDown className="w-4 h-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            {vaults.map((v) => (
              <DropdownMenuItem
                key={v.id}
                onClick={() => onVaultSwitch(v)}
                className={v.id === vault.id ? 'bg-accent' : ''}
              >
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 bg-gradient-to-br from-blue-500 to-purple-600 rounded-sm flex items-center justify-center text-white text-xs font-semibold">
                    {v.name.charAt(0).toUpperCase()}
                  </div>
                  <span className="truncate">{v.name}</span>
                </div>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => setShowVaultCreateDialog(true)}>
              <div className="flex items-center gap-2">
                <Plus className="w-4 h-4" />
                <span>Create New Vault</span>
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleOpenVault}>
              <div className="flex items-center gap-2">
                <FolderOpen className="w-4 h-4" />
                <span>Open Existing Vault</span>
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* File Tree */}
      <div className="flex-1 overflow-auto p-3">
        <div className="mb-4">
          <Button
            variant="ghost"
            size="sm"
            className="w-full justify-start h-8 text-sm font-normal"
            onClick={openCreateDialog}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add page
          </Button>
        </div>

        {filteredTree.length > 0 ? (
          renderFileTree(filteredTree)
        ) : (
          <div className="text-center text-muted-foreground py-8">
            <p>No pages found</p>
            <p className="text-xs mt-2">Create your first page to get started</p>
          </div>
        )}
      </div>

      <Dialog open={showCreateDialog} onOpenChange={(open) => {
        setShowCreateDialog(open)
        if (!open) {
          setSelectedParentFolder(null)
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              Create New Item
              {selectedParentFolder && (
                <span className="text-sm font-normal text-muted-foreground ml-2">
                  in {selectedParentFolder.split('/').pop()}
                </span>
              )}
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Name</label>
              <Input
                placeholder="Enter name"
                value={newFileName}
                onChange={(e) => setNewFileName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleCreateFile()
                }}
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Type</label>
              <Select value={createType} onValueChange={(value: 'markdown' | 'database' | 'folder') => setCreateType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="markdown">
                    <div className="flex items-center gap-2">
                      <FileText className="w-4 h-4" />
                      Page (Markdown)
                    </div>
                  </SelectItem>
                  <SelectItem value="database">
                    <div className="flex items-center gap-2">
                      <Database className="w-4 h-4" />
                      Database (CSV)
                    </div>
                  </SelectItem>
                  <SelectItem value="folder">
                    <div className="flex items-center gap-2">
                      <Folder className="w-4 h-4" />
                      Folder
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowCreateDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateFile}
                disabled={!newFileName.trim()}
              >
                Create
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      <Dialog open={showVaultCreateDialog} onOpenChange={setShowVaultCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Vault</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Vault Name</label>
              <Input
                placeholder="Enter vault name"
                value={newVaultName}
                onChange={(e) => setNewVaultName(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') handleCreateVault()
                }}
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowVaultCreateDialog(false)}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateVault}
                disabled={!newVaultName.trim() || isCreatingVault}
              >
                {isCreatingVault ? 'Creating...' : 'Create'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
