import { useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react'
import { EditorView, ViewUpdate, Decoration, DecorationSet } from '@codemirror/view'
import { EditorState, Extension, StateField, Range } from '@codemirror/state'
import { markdown } from '@codemirror/lang-markdown'
import { history, historyKeymap } from '@codemirror/commands'
import { keymap } from '@codemirror/view'
import { defaultKeymap } from '@codemirror/commands'
import { bracketMatching, indentOnInput } from '@codemirror/language'
import { closeBrackets } from '@codemirror/autocomplete'
import { searchKeymap } from '@codemirror/search'

interface MarkdocHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: KeyboardEvent) => boolean
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface MarkdocHybridEditorRef {
  focus: () => void
}

// Simplified hybrid rendering that actually works
const hybridRenderingField = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes)

    if (tr.docChanged) {
      const doc = tr.state.doc
      const decorationRanges: Range<Decoration>[] = []

      // Process each line for markdown patterns
      for (let i = 1; i <= doc.lines; i++) {
        const line = doc.line(i)
        const text = line.text

        // Headers - apply styling to entire line
        const headerMatch = text.match(/^(#{1,6})\s+(.*)$/)
        if (headerMatch) {
          const level = headerMatch[1].length
          decorationRanges.push(
            Decoration.line({
              class: `hybrid-header-${level}`,
              attributes: { 'data-header-level': level.toString() }
            }).range(line.from)
          )
        }

        // Bold text - style the content, make markers subtle
        let match
        const boldRegex = /\*\*([^*\n]+)\*\*/g
        while ((match = boldRegex.exec(text)) !== null) {
          const start = line.from + match.index
          const end = start + match[0].length
          const contentStart = start + 2
          const contentEnd = end - 2

          // Style the entire bold section
          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-bold' }).range(start, end)
          )
          // Make content bold
          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-bold-content' }).range(contentStart, contentEnd)
          )
        }

        // Italic text - style the content, make markers subtle
        const italicRegex = /(?<!\*)\*([^*\n]+)\*(?!\*)/g
        while ((match = italicRegex.exec(text)) !== null) {
          const start = line.from + match.index
          const end = start + match[0].length
          const contentStart = start + 1
          const contentEnd = end - 1

          // Style the entire italic section
          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-italic' }).range(start, end)
          )
          // Make content italic
          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-italic-content' }).range(contentStart, contentEnd)
          )
        }

        // Inline code - style with background
        const codeRegex = /`([^`\n]+)`/g
        while ((match = codeRegex.exec(text)) !== null) {
          const start = line.from + match.index
          const end = start + match[0].length

          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-code' }).range(start, end)
          )
        }

        // Links - style entire link
        const linkRegex = /\[([^\]]+)\]\(([^)]+)\)/g
        while ((match = linkRegex.exec(text)) !== null) {
          const start = line.from + match.index
          const end = start + match[0].length

          decorationRanges.push(
            Decoration.mark({ class: 'hybrid-link' }).range(start, end)
          )
        }

        // Lists - style entire line
        if (text.match(/^\s*[-*+]\s+/)) {
          decorationRanges.push(
            Decoration.line({ class: 'hybrid-list-item' }).range(line.from)
          )
        }

        // Numbered lists
        if (text.match(/^\s*\d+\.\s+/)) {
          decorationRanges.push(
            Decoration.line({ class: 'hybrid-list-item hybrid-list-numbered' }).range(line.from)
          )
        }

        // Blockquotes
        if (text.match(/^\s*>\s+/)) {
          decorationRanges.push(
            Decoration.line({ class: 'hybrid-blockquote' }).range(line.from)
          )
        }
      }

      decorations = Decoration.set(decorationRanges.sort((a, b) => a.from - b.from))
    }

    return decorations
  },
  provide: f => EditorView.decorations.from(f)
})

// Hybrid editor theme with live rendering styles
const hybridTheme = EditorView.theme({
  '&': {
    color: 'var(--foreground)',
    backgroundColor: 'var(--background)',
    fontSize: '16px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    lineHeight: '1.6',
  },
  '.cm-content': {
    padding: '0',
    caretColor: 'var(--foreground)',
    minHeight: '600px',
  },
  '.cm-focused .cm-content': {
    outline: 'none',
  },
  '.cm-editor': {
    fontSize: '16px',
  },
  '.cm-scroller': {
    fontFamily: 'inherit',
  },
  '.cm-line': {
    padding: '0',
    lineHeight: '1.6',
  },
  // Hybrid header styles - make text large but keep # visible
  '.hybrid-header-1': {
    fontSize: '2em !important',
    fontWeight: '700 !important',
    lineHeight: '1.2 !important',
    marginTop: '1em',
    marginBottom: '0.5em',
    borderBottom: '2px solid var(--border)',
    paddingBottom: '0.3em',
  },
  '.hybrid-header-2': {
    fontSize: '1.5em !important',
    fontWeight: '600 !important',
    lineHeight: '1.3 !important',
    marginTop: '0.8em',
    marginBottom: '0.4em',
    borderBottom: '1px solid var(--border)',
    paddingBottom: '0.3em',
  },
  '.hybrid-header-3': {
    fontSize: '1.25em !important',
    fontWeight: '600 !important',
    lineHeight: '1.4 !important',
    marginTop: '0.6em',
    marginBottom: '0.3em',
  },
  '.hybrid-header-4': {
    fontSize: '1.1em !important',
    fontWeight: '600 !important',
    marginTop: '0.5em',
    marginBottom: '0.2em',
  },
  '.hybrid-header-5': {
    fontSize: '1em !important',
    fontWeight: '600 !important',
    marginTop: '0.4em',
    marginBottom: '0.2em',
  },
  '.hybrid-header-6': {
    fontSize: '0.9em !important',
    fontWeight: '600 !important',
    marginTop: '0.3em',
    marginBottom: '0.1em',
  },
  // Bold text styling - content is bold, markers are subtle
  '.hybrid-bold-content': {
    fontWeight: '600 !important',
  },
  '.hybrid-bold-marker': {
    color: 'var(--muted-foreground)',
    fontWeight: 'normal',
  },
  // Italic text styling - content is italic, markers are subtle
  '.hybrid-italic-content': {
    fontStyle: 'italic !important',
  },
  '.hybrid-italic-marker': {
    color: 'var(--muted-foreground)',
    fontStyle: 'normal',
  },
  // Code styling - content has background, markers are subtle
  '.hybrid-code-content': {
    backgroundColor: 'var(--muted)',
    padding: '2px 4px',
    borderRadius: '3px',
    fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
    fontSize: '0.85em',
  },
  '.hybrid-code-marker': {
    color: 'var(--muted-foreground)',
    backgroundColor: 'var(--muted)',
    padding: '2px 2px',
    borderRadius: '3px',
  },
  // Link styling
  '.hybrid-link': {
    color: 'var(--primary)',
    textDecoration: 'underline',
  },
  // List styling
  '.hybrid-list-item': {
    paddingLeft: '1.5em',
    position: 'relative',
  },
  // Blockquote styling
  '.hybrid-blockquote': {
    borderLeft: '4px solid var(--primary)',
    paddingLeft: '1em',
    marginLeft: '0',
    backgroundColor: 'var(--muted/10)',
    color: 'var(--muted-foreground)',
    fontStyle: 'italic',
  },
  // Selection
  '.cm-selectionBackground, ::selection': {
    backgroundColor: 'var(--accent)',
  },
  '.cm-focused .cm-selectionBackground': {
    backgroundColor: 'var(--accent)',
  },
  // Active line
  '.cm-activeLine': {
    backgroundColor: 'transparent',
  },
  '.cm-activeLineGutter': {
    backgroundColor: 'transparent',
  },
})

// Extensions for the hybrid editor
const hybridExtensions: Extension[] = [
  markdown(),
  history(),
  indentOnInput(),
  bracketMatching(),
  closeBrackets(),
  highlightActiveLine(),
  keymap.of([
    ...defaultKeymap,
    ...historyKeymap,
    ...searchKeymap,
  ]),
  hybridRenderingField,
  hybridTheme,
]

export const MarkdocHybridEditor = forwardRef<MarkdocHybridEditorRef, MarkdocHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const valueRef = useRef(value)

  // Update value ref when prop changes
  useEffect(() => {
    valueRef.current = value
  }, [value])

  // Handle document updates
  const handleUpdate = useCallback((update: ViewUpdate) => {
    if (update.docChanged) {
      const newValue = update.state.doc.toString()
      if (newValue !== valueRef.current) {
        onChange(newValue)
      }
    }
  }, [onChange])

  // Initialize CodeMirror
  useEffect(() => {
    if (!editorRef.current) return

    const state = EditorState.create({
      doc: value,
      extensions: [
        ...hybridExtensions,
        EditorView.updateListener.of(handleUpdate),
      ],
    })

    const view = new EditorView({
      state,
      parent: editorRef.current,
    })

    viewRef.current = view

    // Auto focus if requested
    if (autoFocus) {
      view.focus()
    }

    return () => {
      view.destroy()
      viewRef.current = null
    }
  }, [handleUpdate, autoFocus]) // Only run once on mount

  // Update editor content when value prop changes externally
  useEffect(() => {
    const view = viewRef.current
    if (!view) return

    const currentValue = view.state.doc.toString()
    if (currentValue !== value) {
      view.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: value,
        },
      })
    }
  }, [value])

  const focus = useCallback(() => {
    viewRef.current?.focus()
  }, [])

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  return (
    <div className={`markdoc-hybrid-editor ${className}`}>
      <div
        ref={editorRef}
        className="codemirror-wrapper"
        style={{ minHeight: '600px' }}
      />
    </div>
  )
})
