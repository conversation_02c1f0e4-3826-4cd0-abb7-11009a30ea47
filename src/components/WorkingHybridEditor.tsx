import { useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react'

interface WorkingHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: React.KeyboardEvent<HTMLTextAreaElement>) => void
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface WorkingHybridEditorRef {
  focus: () => void
}

export const WorkingHybridEditor = forwardRef<WorkingHybridEditorRef, WorkingHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const overlayRef = useRef<HTMLDivElement>(null)

  // Apply hybrid styling to the overlay
  const applyHybridStyling = useCallback(() => {
    if (!overlayRef.current || !textareaRef.current) return

    const text = textareaRef.current.value
    const lines = text.split('\n')
    
    const styledLines = lines.map(line => {
      let styledLine = line

      // Headers - make them large but keep # visible
      if (line.startsWith('# ')) {
        styledLine = `<div class="hybrid-h1">${line}</div>`
      } else if (line.startsWith('## ')) {
        styledLine = `<div class="hybrid-h2">${line}</div>`
      } else if (line.startsWith('### ')) {
        styledLine = `<div class="hybrid-h3">${line}</div>`
      } else {
        // Bold text - make content bold but keep ** visible
        styledLine = styledLine.replace(/\*\*([^*]+)\*\*/g, (match, content) => {
          return `<span class="hybrid-bold-marker">**</span><span class="hybrid-bold-content">${content}</span><span class="hybrid-bold-marker">**</span>`
        })

        // Italic text - make content italic but keep * visible
        styledLine = styledLine.replace(/(?<!\*)\*([^*]+)\*(?!\*)/g, (match, content) => {
          return `<span class="hybrid-italic-marker">*</span><span class="hybrid-italic-content">${content}</span><span class="hybrid-italic-marker">*</span>`
        })

        // Code - add background but keep ` visible
        styledLine = styledLine.replace(/`([^`]+)`/g, (match, content) => {
          return `<span class="hybrid-code-marker">\`</span><span class="hybrid-code-content">${content}</span><span class="hybrid-code-marker">\`</span>`
        })

        styledLine = `<div class="hybrid-line">${styledLine}</div>`
      }

      return styledLine
    })

    overlayRef.current.innerHTML = styledLines.join('')
  }, [])

  // Update overlay when content changes
  useEffect(() => {
    applyHybridStyling()
  }, [value, applyHybridStyling])

  // Handle input changes
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    onChange(e.target.value)
  }

  // Sync scroll between textarea and overlay
  const handleScroll = () => {
    if (textareaRef.current && overlayRef.current) {
      overlayRef.current.scrollTop = textareaRef.current.scrollTop
      overlayRef.current.scrollLeft = textareaRef.current.scrollLeft
    }
  }

  const focus = useCallback(() => {
    textareaRef.current?.focus()
  }, [])

  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  return (
    <div className={`working-hybrid-editor ${className}`}>
      <div className="hybrid-container">
        {/* Styled overlay */}
        <div
          ref={overlayRef}
          className="hybrid-overlay"
          onScroll={handleScroll}
        />
        
        {/* Transparent textarea */}
        <textarea
          ref={textareaRef}
          value={value}
          onChange={handleChange}
          onKeyDown={onKeyDown}
          onScroll={handleScroll}
          className="hybrid-textarea"
          placeholder={placeholder}
          spellCheck={false}
          autoFocus={autoFocus}
        />
      </div>
    </div>
  )
})
