import { useState, useEffect } from 'react'
import { Vault, FileSystemItem } from '../types'
import { Sidebar } from './Sidebar'
import { MainContent } from './MainContent'

interface MainLayoutProps {
  vault: Vault
  vaults: Vault[]
  onVaultClose: () => void
  onVaultSwitch: (vault: Vault) => void
  onVaultCreate?: (vault: Vault) => void
}

export function MainLayout({ vault, vaults, onVaultClose, onVaultSwitch, onVaultCreate }: MainLayoutProps) {
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [fileTree, setFileTree] = useState<FileSystemItem[]>([])

  // Load vault file structure
  useEffect(() => {
    loadFileTree()
    migrateOldVaultStructure()
  }, [vault])

  const migrateOldVaultStructure = async () => {
    try {
      const items = await window.electronAPI.readDirectory(vault.path)
      const notesFolder = items.find(item => item.name === 'Notes' && item.isDirectory)
      const databasesFolder = items.find(item => item.name === 'Databases' && item.isDirectory)

      // Migrate files from Notes folder
      if (notesFolder) {
        const notesItems = await window.electronAPI.readDirectory(notesFolder.path)
        for (const item of notesItems) {
          if (!item.isDirectory) {
            const oldPath = item.path
            const newPath = `${vault.path}/${item.name}`
            try {
              const content = await window.electronAPI.readFile(oldPath)
              await window.electronAPI.writeFile(newPath, content)
              await window.electronAPI.deleteFile(oldPath)
            } catch (error) {
              console.error('Error migrating file:', error)
            }
          }
        }
        // Try to remove empty Notes folder
        try {
          await window.electronAPI.deleteDirectory(notesFolder.path)
        } catch (error) {
          // Ignore if folder is not empty
        }
      }

      // Migrate files from Databases folder
      if (databasesFolder) {
        const databaseItems = await window.electronAPI.readDirectory(databasesFolder.path)
        for (const item of databaseItems) {
          if (!item.isDirectory) {
            const oldPath = item.path
            const newPath = `${vault.path}/${item.name}`
            try {
              const content = await window.electronAPI.readFile(oldPath)
              await window.electronAPI.writeFile(newPath, content)
              await window.electronAPI.deleteFile(oldPath)
            } catch (error) {
              console.error('Error migrating file:', error)
            }
          }
        }
        // Try to remove empty Databases folder
        try {
          await window.electronAPI.deleteDirectory(databasesFolder.path)
        } catch (error) {
          // Ignore if folder is not empty
        }
      }
    } catch (error) {
      console.error('Error during migration:', error)
    }
  }

  const loadFileTree = async () => {
    try {
      const items = await window.electronAPI.readDirectory(vault.path)
      const tree = await buildFileTree(items)
      setFileTree(tree)
    } catch (error) {
      console.error('Error loading file tree:', error)
    }
  }

  const buildFileTree = async (items: any[]): Promise<FileSystemItem[]> => {
    const tree: FileSystemItem[] = []

    for (const item of items) {
      const fileItem: FileSystemItem = {
        name: item.name,
        path: item.path,
        isDirectory: item.isDirectory,
      }

      if (item.isDirectory) {
        try {
          const children = await window.electronAPI.readDirectory(item.path)
          fileItem.children = await buildFileTree(children)
        } catch (error) {
          console.error(`Error reading directory ${item.path}:`, error)
          fileItem.children = []
        }
      }

      tree.push(fileItem)
    }

    return tree.sort((a, b) => {
      // Directories first, then files
      if (a.isDirectory && !b.isDirectory) return -1
      if (!a.isDirectory && b.isDirectory) return 1
      return a.name.localeCompare(b.name)
    })
  }

  const handleFileSelect = (filePath: string) => {
    setSelectedFile(filePath)
  }

  const handleFileCreate = async (name: string, isDirectory: boolean, parentPath?: string) => {
    try {
      const targetPath = parentPath || vault.path
      const fullPath = `${targetPath}/${name}`

      if (isDirectory) {
        await window.electronAPI.createDirectory(fullPath)
      } else {
        await window.electronAPI.writeFile(fullPath, '')
      }

      // Reload file tree
      await loadFileTree()

      // Select the new file if it's not a directory
      if (!isDirectory) {
        setSelectedFile(fullPath)
      }
    } catch (error) {
      console.error('Error creating file/directory:', error)
    }
  }

  const handleFileDelete = async (filePath: string, isDirectory: boolean) => {
    try {
      if (isDirectory) {
        await window.electronAPI.deleteDirectory(filePath)
      } else {
        await window.electronAPI.deleteFile(filePath)
      }

      // Clear selection if deleted file was selected
      if (selectedFile === filePath) {
        setSelectedFile(null)
      }

      // Reload file tree
      await loadFileTree()
    } catch (error) {
      console.error('Error deleting file/directory:', error)
    }
  }

  const handleFileMove = async (sourcePath: string, targetFolderPath: string) => {
    try {
      const fileName = sourcePath.split('/').pop()
      if (!fileName) return

      const targetPath = `${targetFolderPath}/${fileName}`

      // Don't move if source and target are the same
      if (sourcePath === targetPath) return

      await window.electronAPI.moveFile(sourcePath, targetPath)

      // Update selection if moved file was selected
      if (selectedFile === sourcePath) {
        setSelectedFile(targetPath)
      }

      // Reload file tree
      await loadFileTree()
    } catch (error) {
      console.error('Error moving file:', error)
    }
  }

  const handleFileRename = async (oldPath: string, newName: string) => {
    try {
      const directory = oldPath.substring(0, oldPath.lastIndexOf('/'))
      const newPath = `${directory}/${newName}`

      // Don't rename if name is the same
      if (oldPath === newPath) return

      await window.electronAPI.moveFile(oldPath, newPath)

      // Update selection if renamed file was selected
      if (selectedFile === oldPath) {
        setSelectedFile(newPath)
      }

      // Reload file tree
      await loadFileTree()
    } catch (error) {
      console.error('Error renaming file:', error)
      throw error // Re-throw so the editor can handle it
    }
  }

  return (
    <div className="app-layout">
      <Sidebar
        vault={vault}
        vaults={vaults}
        fileTree={fileTree}
        selectedFile={selectedFile}
        onFileSelect={handleFileSelect}
        onFileCreate={handleFileCreate}
        onFileDelete={handleFileDelete}
        onFileMove={handleFileMove}
        onVaultClose={onVaultClose}
        onVaultSwitch={onVaultSwitch}
        onVaultCreate={onVaultCreate}
      />
      <MainContent
        selectedFile={selectedFile}
        onFileChange={loadFileTree}
        onFileRename={handleFileRename}
      />
    </div>
  )
}
