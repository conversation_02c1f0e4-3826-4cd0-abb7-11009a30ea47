import { useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react'
import { EditorView, ViewUpdate, Decoration, DecorationSet } from '@codemirror/view'
import { EditorState, Extension, StateField, Range } from '@codemirror/state'
import { markdown } from '@codemirror/lang-markdown'
import { history, historyKeymap } from '@codemirror/commands'
import { keymap } from '@codemirror/view'
import { defaultKeymap } from '@codemirror/commands'
import { bracketMatching, indentOnInput } from '@codemirror/language'
import { closeBrackets } from '@codemirror/autocomplete'

interface SimpleHybridEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: KeyboardEvent) => boolean
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface SimpleHybridEditorRef {
  focus: () => void
}

// Working hybrid decorations
const hybridDecorations = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes)

    if (tr.docChanged) {
      const doc = tr.state.doc
      const decorationRanges: Range<Decoration>[] = []

      // Process each line
      for (let i = 1; i <= doc.lines; i++) {
        const line = doc.line(i)
        const text = line.text

        // Headers
        if (text.startsWith('# ')) {
          decorationRanges.push(
            Decoration.line({
              class: 'hybrid-h1',
              attributes: { style: 'font-size: 2em; font-weight: 700; line-height: 1.2; margin: 0.5em 0; border-bottom: 2px solid var(--border); padding-bottom: 0.3em;' }
            }).range(line.from)
          )
        } else if (text.startsWith('## ')) {
          decorationRanges.push(
            Decoration.line({
              class: 'hybrid-h2',
              attributes: { style: 'font-size: 1.5em; font-weight: 600; line-height: 1.3; margin: 0.4em 0; border-bottom: 1px solid var(--border); padding-bottom: 0.3em;' }
            }).range(line.from)
          )
        } else if (text.startsWith('### ')) {
          decorationRanges.push(
            Decoration.line({
              class: 'hybrid-h3',
              attributes: { style: 'font-size: 1.25em; font-weight: 600; line-height: 1.4; margin: 0.3em 0;' }
            }).range(line.from)
          )
        }

        // Bold text
        let match
        const boldRegex = /\*\*([^*\n]+)\*\*/g
        while ((match = boldRegex.exec(text)) !== null) {
          const start = line.from + match.index + 2
          const end = start + match[1].length
          decorationRanges.push(
            Decoration.mark({
              class: 'hybrid-bold',
              attributes: { style: 'font-weight: 700;' }
            }).range(start, end)
          )
        }

        // Italic text
        const italicRegex = /(?<!\*)\*([^*\n]+)\*(?!\*)/g
        while ((match = italicRegex.exec(text)) !== null) {
          const start = line.from + match.index + 1
          const end = start + match[1].length
          decorationRanges.push(
            Decoration.mark({
              class: 'hybrid-italic',
              attributes: { style: 'font-style: italic;' }
            }).range(start, end)
          )
        }

        // Code
        const codeRegex = /`([^`\n]+)`/g
        while ((match = codeRegex.exec(text)) !== null) {
          const start = line.from + match.index
          const end = start + match[0].length
          decorationRanges.push(
            Decoration.mark({
              class: 'hybrid-code',
              attributes: { style: 'background-color: var(--muted); padding: 2px 4px; border-radius: 3px; font-family: monospace; font-size: 0.85em;' }
            }).range(start, end)
          )
        }
      }

      decorations = Decoration.set(decorationRanges.sort((a, b) => a.from - b.from))
    }

    return decorations
  },
  provide: f => EditorView.decorations.from(f)
})

// Simple theme that makes markdown look good
const hybridTheme = EditorView.theme({
  '&': {
    color: 'var(--foreground)',
    backgroundColor: 'var(--background)',
    fontSize: '16px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    lineHeight: '1.6',
  },
  '.cm-content': {
    padding: '0',
    caretColor: 'var(--foreground)',
    minHeight: '600px',
  },
  '.cm-focused .cm-content': {
    outline: 'none',
  },
  '.cm-editor': {
    fontSize: '16px',
  },
  '.cm-scroller': {
    fontFamily: 'inherit',
  },
  '.cm-line': {
    padding: '0',
    lineHeight: '1.6',
  },
  // Make headers large immediately when typed
  '.cm-line:has-text("# ")': {
    fontSize: '2em !important',
    fontWeight: '700 !important',
    lineHeight: '1.2 !important',
  },
  '.cm-line:has-text("## ")': {
    fontSize: '1.5em !important',
    fontWeight: '600 !important',
    lineHeight: '1.3 !important',
  },
  '.cm-line:has-text("### ")': {
    fontSize: '1.25em !important',
    fontWeight: '600 !important',
    lineHeight: '1.4 !important',
  },
  // Selection
  '.cm-selectionBackground, ::selection': {
    backgroundColor: 'var(--accent)',
  },
  '.cm-focused .cm-selectionBackground': {
    backgroundColor: 'var(--accent)',
  },
})

// Basic extensions with hybrid decorations
const extensions: Extension[] = [
  markdown(),
  history(),
  indentOnInput(),
  bracketMatching(),
  closeBrackets(),
  keymap.of([
    ...defaultKeymap,
    ...historyKeymap,
  ]),
  hybridDecorations,
  hybridTheme,
]

export const SimpleHybridEditor = forwardRef<SimpleHybridEditorRef, SimpleHybridEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const valueRef = useRef(value)

  // Update value ref when prop changes
  useEffect(() => {
    valueRef.current = value
  }, [value])

  // Handle document updates
  const handleUpdate = useCallback((update: ViewUpdate) => {
    if (update.docChanged) {
      const newValue = update.state.doc.toString()
      if (newValue !== valueRef.current) {
        onChange(newValue)
      }
    }
  }, [onChange])

  // Initialize CodeMirror
  useEffect(() => {
    if (!editorRef.current) return

    const state = EditorState.create({
      doc: value,
      extensions: [
        ...extensions,
        EditorView.updateListener.of(handleUpdate),
      ],
    })

    const view = new EditorView({
      state,
      parent: editorRef.current,
    })

    viewRef.current = view

    // Auto focus if requested
    if (autoFocus) {
      view.focus()
    }

    return () => {
      view.destroy()
      viewRef.current = null
    }
  }, [handleUpdate, autoFocus])

  // Update editor content when value prop changes externally
  useEffect(() => {
    const view = viewRef.current
    if (!view) return

    const currentValue = view.state.doc.toString()
    if (currentValue !== value) {
      view.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: value,
        },
      })
    }
  }, [value])

  const focus = useCallback(() => {
    viewRef.current?.focus()
  }, [])

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    focus,
  }), [focus])

  return (
    <div className={`simple-hybrid-editor ${className}`}>
      <div
        ref={editorRef}
        className="codemirror-wrapper"
        style={{ minHeight: '600px' }}
      />
    </div>
  )
})
