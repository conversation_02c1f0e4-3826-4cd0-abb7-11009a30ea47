import { useState, useEffect } from 'react'
import { Button } from './ui/button'
import { Input } from './ui/input'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table'
import { parseCSV, stringifyCSV } from '../lib/utils'
import { Plus, Edit, Trash2, Save, X, Database } from 'lucide-react'

interface DatabaseTableProps {
  content: string
  onContentChange: (newContent: string) => void
  isDirty: boolean
  onSave: () => void
}

export function DatabaseTable({ content, onContentChange, isDirty, onSave }: DatabaseTableProps) {
  const [data, setData] = useState<string[][]>([])
  const [editingCell, setEditingCell] = useState<{ row: number; col: number } | null>(null)
  const [editValue, setEditValue] = useState('')

  useEffect(() => {
    const parsed = parseCSV(content)
    if (parsed.length === 0) {
      // Create a default table structure
      setData([
        ['Name', 'Type', 'Status'],
        ['', '', '']
      ])
    } else {
      setData(parsed)
    }
  }, [content])

  const updateData = (newData: string[][]) => {
    setData(newData)
    const csvContent = stringifyCSV(newData)
    onContentChange(csvContent)
  }

  const handleCellEdit = (rowIndex: number, colIndex: number) => {
    setEditingCell({ row: rowIndex, col: colIndex })
    setEditValue(data[rowIndex]?.[colIndex] || '')
  }

  const handleCellSave = () => {
    if (!editingCell) return

    const newData = [...data]
    if (!newData[editingCell.row]) {
      newData[editingCell.row] = []
    }
    newData[editingCell.row][editingCell.col] = editValue

    updateData(newData)
    setEditingCell(null)
    setEditValue('')
  }

  const handleCellCancel = () => {
    setEditingCell(null)
    setEditValue('')
  }

  const addRow = () => {
    const newData = [...data]
    const columnCount = Math.max(...newData.map(row => row.length))
    const newRow = new Array(columnCount).fill('')
    newData.push(newRow)
    updateData(newData)
  }

  const addColumn = () => {
    const newData = data.map(row => [...row, ''])
    updateData(newData)
  }

  const deleteRow = (rowIndex: number) => {
    if (data.length <= 1) return // Keep at least header
    const newData = data.filter((_, index) => index !== rowIndex)
    updateData(newData)
  }

  const deleteColumn = (colIndex: number) => {
    if (data[0]?.length <= 1) return // Keep at least one column
    const newData = data.map(row => row.filter((_, index) => index !== colIndex))
    updateData(newData)
  }

  const renderCell = (cellValue: string, rowIndex: number, colIndex: number) => {
    const isEditing = editingCell?.row === rowIndex && editingCell?.col === colIndex
    const isHeader = rowIndex === 0

    if (isEditing) {
      return (
        <div className="flex items-center gap-1">
          <Input
            value={editValue}
            onChange={(e) => setEditValue(e.target.value)}
            onKeyDown={(e) => {
              if (e.key === 'Enter') handleCellSave()
              if (e.key === 'Escape') handleCellCancel()
            }}
            className="h-8 text-xs"
            autoFocus
          />
          <Button size="sm" variant="ghost" onClick={handleCellSave}>
            <Save className="w-3 h-3" />
          </Button>
          <Button size="sm" variant="ghost" onClick={handleCellCancel}>
            <X className="w-3 h-3" />
          </Button>
        </div>
      )
    }

    return (
      <div
        className={`cursor-pointer hover:bg-muted/50 p-1 rounded min-h-[24px] ${
          isHeader ? 'font-medium' : ''
        }`}
        onClick={() => handleCellEdit(rowIndex, colIndex)}
      >
        {cellValue || (
          <span className="text-muted-foreground text-xs">Click to edit</span>
        )}
      </div>
    )
  }

  return (
    <div className="h-full flex flex-col bg-background">
      {/* Header - Notion style */}
      <div className="flex items-center justify-between p-6 border-b border-border/50">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-muted rounded-md flex items-center justify-center">
            <Database className="w-4 h-4" />
          </div>
          <div>
            <h1 className="text-xl font-semibold">Database</h1>
            {isDirty && <span className="text-sm text-muted-foreground">Unsaved changes</span>}
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button size="sm" variant="outline" onClick={addColumn}>
            <Plus className="w-4 h-4 mr-1" />
            Property
          </Button>
          <Button size="sm" variant="outline" onClick={addRow}>
            <Plus className="w-4 h-4 mr-1" />
            New
          </Button>
          {isDirty && (
            <Button size="sm" onClick={onSave}>
              Save
            </Button>
          )}
        </div>
      </div>

      {/* Table - Notion style */}
      <div className="flex-1 overflow-auto">
        <div className="min-w-full">
          <Table className="notion-table">
            <TableHeader>
              <TableRow className="border-b border-border/50">
                <TableHead className="w-12 bg-muted/30 sticky left-0 z-10">#</TableHead>
                {data[0]?.map((header, colIndex) => (
                  <TableHead key={colIndex} className="min-w-[200px] bg-muted/30 font-medium">
                    <div className="flex items-center justify-between group">
                      <div className="flex-1">
                        {renderCell(header, 0, colIndex)}
                      </div>
                      {data[0].length > 1 && (
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => deleteColumn(colIndex)}
                          className="ml-2 h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      )}
                    </div>
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.slice(1).map((row, rowIndex) => (
                <TableRow key={rowIndex + 1} className="border-b border-border/30 hover:bg-muted/20 group">
                  <TableCell className="font-mono text-xs text-muted-foreground bg-muted/10 sticky left-0 z-10">
                    <div className="flex items-center gap-1">
                      <span className="w-6 text-center">{rowIndex + 1}</span>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => deleteRow(rowIndex + 1)}
                        className="h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                      >
                        <Trash2 className="w-3 h-3" />
                      </Button>
                    </div>
                  </TableCell>
                  {data[0]?.map((_, colIndex) => (
                    <TableCell key={colIndex} className="p-0">
                      <div className="p-3 min-h-[44px] flex items-center">
                        {renderCell(row[colIndex] || '', rowIndex + 1, colIndex)}
                      </div>
                    </TableCell>
                  ))}
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {data.length <= 1 && (
            <div className="text-center py-12 text-muted-foreground">
              <Database className="w-12 h-12 mx-auto mb-4 opacity-30" />
              <p className="text-lg font-medium mb-2">No entries yet</p>
              <p className="text-sm">Click "New" to add your first entry</p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
