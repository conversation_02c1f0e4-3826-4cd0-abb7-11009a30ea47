import { useState } from 'react'
import { But<PERSON> } from './ui/button'
import { Input } from './ui/input'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogDescription } from './ui/dialog'
import { Vault } from '../types'
import { FolderOpen, Plus, Folder } from 'lucide-react'
import { formatDate, generateId } from '../lib/utils'

interface VaultSelectorProps {
  vaults: Vault[]
  onVaultSelect: (vault: Vault) => void
  onVaultCreate: (vault: Vault) => void
}

export function VaultSelector({ vaults, onVaultSelect, onVaultCreate }: VaultSelectorProps) {
  const [showCreateDialog, setShowCreateDialog] = useState(false)
  const [newVaultName, setNewVaultName] = useState('')
  const [isCreating, setIsCreating] = useState(false)

  const handleOpenVault = async () => {
    try {
      const selectedPath = await window.electronAPI.selectDirectory()
      if (selectedPath) {
        const vaultName = selectedPath.split('/').pop() || 'Unnamed Vault'
        const vault: Vault = {
          id: generateId(),
          name: vaultName,
          path: selectedPath,
          lastOpened: new Date()
        }
        onVaultSelect(vault)
      }
    } catch (error) {
      console.error('Error opening vault:', error)
    }
  }

  const handleCreateVault = async () => {
    if (!newVaultName.trim()) return

    setIsCreating(true)
    try {
      const selectedPath = await window.electronAPI.selectDirectory()
      if (selectedPath) {
        const vaultPath = `${selectedPath}/${newVaultName}`

        // Create the vault directory
        await window.electronAPI.createDirectory(vaultPath)

        // Create a welcome page
        const welcomeContent = `# Welcome to ${newVaultName}

This is your new KaptureHQ vault!

## Getting Started

- Create pages using the "New Page" button in the sidebar
- Create databases using the "New Database" button
- Each page can be either markdown content or a structured database
- All your pages are stored as files in this vault folder

Happy organizing! 📝`

        await window.electronAPI.writeFile(`${vaultPath}/Welcome.md`, welcomeContent)

        const vault: Vault = {
          id: generateId(),
          name: newVaultName,
          path: vaultPath,
          lastOpened: new Date()
        }

        onVaultCreate(vault)
        setShowCreateDialog(false)
        setNewVaultName('')
      }
    } catch (error) {
      console.error('Error creating vault:', error)
    } finally {
      setIsCreating(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background p-8">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold mb-2">KaptureHQ</h1>
          <p className="text-muted-foreground text-lg">
            Your local-first knowledge management system
          </p>
        </div>

        <div className="bg-card border rounded-lg p-6 shadow-sm">
          <div className="flex gap-4 mb-6">
            <Button onClick={() => setShowCreateDialog(true)} className="flex-1">
              <Plus className="w-4 h-4 mr-2" />
              Create New Vault
            </Button>
            <Button variant="outline" onClick={handleOpenVault} className="flex-1">
              <FolderOpen className="w-4 h-4 mr-2" />
              Open Existing Vault
            </Button>
          </div>

          {vaults.length > 0 && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Recent Vaults</h3>
              <div className="space-y-2">
                {vaults
                  .sort((a, b) => new Date(b.lastOpened).getTime() - new Date(a.lastOpened).getTime())
                  .map((vault) => (
                    <div
                      key={vault.id}
                      className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent cursor-pointer"
                      onClick={() => onVaultSelect(vault)}
                    >
                      <div className="flex items-center gap-3">
                        <Folder className="w-5 h-5 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{vault.name}</div>
                          <div className="text-sm text-muted-foreground">{vault.path}</div>
                        </div>
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {formatDate(vault.lastOpened)}
                      </div>
                    </div>
                  ))}
              </div>
            </div>
          )}
        </div>
      </div>

      <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Create New Vault</DialogTitle>
            <DialogDescription>
              Choose a name for your new vault. A folder will be created with this name.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <Input
              placeholder="Vault name"
              value={newVaultName}
              onChange={(e) => setNewVaultName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !isCreating) {
                  handleCreateVault()
                }
              }}
            />
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowCreateDialog(false)}
                disabled={isCreating}
              >
                Cancel
              </Button>
              <Button
                onClick={handleCreateVault}
                disabled={!newVaultName.trim() || isCreating}
              >
                {isCreating ? 'Creating...' : 'Create Vault'}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}
