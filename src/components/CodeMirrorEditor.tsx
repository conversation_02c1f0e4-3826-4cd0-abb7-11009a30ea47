import { useEffect, useRef, useCallback, forwardRef, useImperativeHandle } from 'react'
import { EditorView } from '@codemirror/view'
import { EditorState, Transaction } from '@codemirror/state'
import { hybridExtensions, createEditorState } from '../lib/codemirror-config'
import { wrapSelectedText, insertLinePrefix, insertTextAtCursor } from '../lib/utils'

interface CodeMirrorEditorProps {
  value: string
  onChange: (value: string) => void
  onKeyDown?: (event: KeyboardEvent) => boolean
  placeholder?: string
  className?: string
  autoFocus?: boolean
}

export interface CodeMirrorEditorRef {
  formatBold: () => void
  formatItalic: () => void
  formatCode: () => void
  formatHeader: (level: number) => void
  formatList: (ordered?: boolean) => void
  insertLink: () => void
  insertHorizontalRule: () => void
}

export const CodeMirrorEditor = forwardRef<CodeMirrorEditorRef, CodeMirrorEditorProps>(({
  value,
  onChange,
  onKeyDown,
  placeholder = 'Start writing your thoughts...',
  className = '',
  autoFocus = false,
}, ref) => {
  const editorRef = useRef<HTMLDivElement>(null)
  const viewRef = useRef<EditorView | null>(null)
  const valueRef = useRef(value)

  // Update value ref when prop changes
  useEffect(() => {
    valueRef.current = value
  }, [value])

  // Handle document updates
  const handleUpdate = useCallback((transaction: Transaction) => {
    if (transaction.docChanged) {
      const newValue = transaction.state.doc.toString()
      if (newValue !== valueRef.current) {
        onChange(newValue)
      }
    }
  }, [onChange])

  // Initialize CodeMirror
  useEffect(() => {
    if (!editorRef.current) return

    const state = createEditorState(value, hybridExtensions)
    const view = new EditorView({
      state,
      parent: editorRef.current,
      dispatch: (transaction) => {
        view.update([transaction])
        handleUpdate(transaction)
      },
    })

    viewRef.current = view

    // Auto focus if requested
    if (autoFocus) {
      view.focus()
    }

    return () => {
      view.destroy()
      viewRef.current = null
    }
  }, []) // Only run once on mount

  // Update editor content when value prop changes externally
  useEffect(() => {
    const view = viewRef.current
    if (!view) return

    const currentValue = view.state.doc.toString()
    if (currentValue !== value) {
      view.dispatch({
        changes: {
          from: 0,
          to: currentValue.length,
          insert: value,
        },
      })
    }
  }, [value])

  // Expose formatting methods for toolbar integration
  const formatBold = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `**${selectedText}**`

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 2, head: from + 2 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatItalic = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `*${selectedText}*`

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatCode = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const newText = `\`${selectedText}\``

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + 1, head: from + 1 + selectedText.length },
    })
    view.focus()
  }, [])

  const formatHeader = useCallback((level: number) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const prefix = '#'.repeat(level) + ' '

    // Remove existing header prefix if any
    const cleanText = lineText.replace(/^#+\s*/, '')
    const newText = prefix + cleanText

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + prefix.length, head: line.from + newText.length },
    })
    view.focus()
  }, [])

  const formatList = useCallback((ordered: boolean = false) => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const lineText = line.text
    const prefix = ordered ? '1. ' : '- '

    // Remove existing list prefix if any
    const cleanText = lineText.replace(/^(\d+\.\s*|-\s*|\*\s*)/, '')
    const newText = prefix + cleanText

    view.dispatch({
      changes: { from: line.from, to: line.to, insert: newText },
      selection: { anchor: line.from + prefix.length, head: line.from + newText.length },
    })
    view.focus()
  }, [])

  const insertLink = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from, to } = view.state.selection.main
    const selectedText = view.state.doc.sliceString(from, to)
    const linkText = selectedText || 'link text'
    const newText = `[${linkText}](url)`

    view.dispatch({
      changes: { from, to, insert: newText },
      selection: { anchor: from + linkText.length + 3, head: from + linkText.length + 6 },
    })
    view.focus()
  }, [])

  const insertHorizontalRule = useCallback(() => {
    const view = viewRef.current
    if (!view) return

    const { from } = view.state.selection.main
    const line = view.state.doc.lineAt(from)
    const newText = '\n---\n'

    view.dispatch({
      changes: { from: line.to, insert: newText },
      selection: { anchor: line.to + newText.length },
    })
    view.focus()
  }, [])

  // Expose methods to parent component via ref
  useImperativeHandle(ref, () => ({
    formatBold,
    formatItalic,
    formatCode,
    formatHeader,
    formatList,
    insertLink,
    insertHorizontalRule,
  }), [formatBold, formatItalic, formatCode, formatHeader, formatList, insertLink, insertHorizontalRule])

  return (
    <div
      ref={editorRef}
      className={`codemirror-editor ${className}`}
      style={{ minHeight: '600px' }}
    />
  )
})
