import { useState, useRef } from 'react'
import { ObsidianMarkdownEditor, ObsidianMarkdownEditorRef } from './ObsidianMarkdownEditor'
import { But<PERSON> } from './ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card'
import { Badge } from './ui/badge'
import { 
  FileText, 
  Keyboard, 
  Palette, 
  Zap, 
  Eye, 
  Code, 
  Save,
  RotateCcw
} from 'lucide-react'

const DEMO_CONTENT = `# Welcome to the Obsidian-Style Markdown Editor

This is a demonstration of our enhanced markdown editor that closely mimics Obsidian's interface and functionality.

## Key Features

### 🎨 **Obsidian-Style Design**
- Exact CSS styling matching Obsidian's interface
- Proper cursor blinking animations
- Selection highlighting
- Active line highlighting

### ⌨️ **Keyboard Shortcuts**
- **Ctrl+B** - Bold text
- **Ctrl+I** - Italic text
- **Ctrl+E** - Inline code
- **Ctrl+K** - Insert link
- **Ctrl+1/2/3** - Headers
- **Ctrl+Shift+8** - Bullet list
- **Ctrl+Shift+7** - Numbered list
- **Ctrl+S** - Save

### 🚀 **Advanced Features**
- Auto-save functionality (2 seconds after changes)
- Live syntax highlighting
- Real-time markdown rendering
- Toolbar with formatting buttons
- Title editing with file rename support

## Markdown Examples

### Text Formatting
This is **bold text** and this is *italic text*.
You can also use \`inline code\` and ~~strikethrough~~.

### Lists
- Bullet point 1
- Bullet point 2
  - Nested item
  - Another nested item

1. Numbered item 1
2. Numbered item 2
3. Numbered item 3

### Quotes
> This is a blockquote
> It can span multiple lines
> And looks great!

### Code Blocks
\`\`\`javascript
function hello() {
  console.log("Hello, Obsidian-style editor!");
}
\`\`\`

### Links and Images
[This is a link](https://example.com)
![Alt text for image](https://via.placeholder.com/300x200)

---

## Try It Out!

1. **Edit the title** - Click on the title above to rename it
2. **Use keyboard shortcuts** - Try Ctrl+B to make text bold
3. **Use the toolbar** - Click the formatting buttons
4. **Auto-save** - Changes are automatically saved after 2 seconds
5. **Toggle toolbar** - Click the ⋯ button to hide/show the toolbar

Start typing below to experience the smooth, Obsidian-like editing experience!

`

export function ObsidianEditorDemo() {
  const [content, setContent] = useState(DEMO_CONTENT)
  const [isDirty, setIsDirty] = useState(false)
  const [fileName, setFileName] = useState('Demo Document.md')
  const [saveCount, setSaveCount] = useState(0)
  const editorRef = useRef<ObsidianMarkdownEditorRef>(null)

  const handleContentChange = (newContent: string) => {
    setContent(newContent)
    setIsDirty(true)
  }

  const handleSave = () => {
    setIsDirty(false)
    setSaveCount(prev => prev + 1)
    console.log('Document saved!', { content, fileName })
  }

  const handleFileRename = (oldPath: string, newName: string) => {
    setFileName(newName)
    console.log('File renamed:', { oldPath, newName })
  }

  const resetDemo = () => {
    setContent(DEMO_CONTENT)
    setFileName('Demo Document.md')
    setIsDirty(false)
    setSaveCount(0)
  }

  const insertSampleText = () => {
    editorRef.current?.focus()
    // You could add more sample text insertion logic here
  }

  return (
    <div className="h-screen flex flex-col bg-background">
      {/* Demo Header */}
      <div className="border-b bg-muted/30 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold flex items-center gap-2">
                <FileText className="h-6 w-6" />
                Obsidian-Style Markdown Editor Demo
              </h1>
              <p className="text-muted-foreground mt-1">
                Experience the power of our enhanced markdown editor
              </p>
            </div>
            <div className="flex items-center gap-2">
              <Badge variant={isDirty ? "destructive" : "secondary"}>
                {isDirty ? "Unsaved Changes" : "Saved"}
              </Badge>
              <Badge variant="outline">
                Saves: {saveCount}
              </Badge>
              <Button variant="outline" size="sm" onClick={resetDemo}>
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset Demo
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="border-b bg-muted/10 p-4">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card className="p-3">
              <div className="flex items-center gap-2">
                <Palette className="h-4 w-4 text-blue-500" />
                <div>
                  <div className="font-medium text-sm">Obsidian Styling</div>
                  <div className="text-xs text-muted-foreground">Exact UI match</div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center gap-2">
                <Keyboard className="h-4 w-4 text-green-500" />
                <div>
                  <div className="font-medium text-sm">Keyboard Shortcuts</div>
                  <div className="text-xs text-muted-foreground">Full support</div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-yellow-500" />
                <div>
                  <div className="font-medium text-sm">Auto-save</div>
                  <div className="text-xs text-muted-foreground">2s delay</div>
                </div>
              </div>
            </Card>
            <Card className="p-3">
              <div className="flex items-center gap-2">
                <Eye className="h-4 w-4 text-purple-500" />
                <div>
                  <div className="font-medium text-sm">Live Preview</div>
                  <div className="text-xs text-muted-foreground">Real-time</div>
                </div>
              </div>
            </Card>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 overflow-hidden">
        <ObsidianMarkdownEditor
          ref={editorRef}
          content={content}
          onChange={handleContentChange}
          onSave={handleSave}
          isDirty={isDirty}
          fileName={fileName}
          onFileRename={handleFileRename}
          filePath={`/demo/${fileName}`}
          autoFocus={true}
          placeholder="Start writing your thoughts..."
        />
      </div>
    </div>
  )
}
