import { Button } from './ui/button'
import { 
  Bold, 
  Italic, 
  Code, 
  List, 
  ListOrdered, 
  Quote, 
  Heading1, 
  Heading2, 
  Heading3,
  Link,
  Image,
  Minus
} from 'lucide-react'
import { wrapSelectedText, insertLinePrefix, insertTextAtCursor } from '../lib/utils'

interface MarkdownToolbarProps {
  textareaRef: React.RefObject<HTMLTextAreaElement>
  onContentChange: (content: string) => void
}

export function MarkdownToolbar({ textareaRef, onContentChange }: MarkdownToolbarProps) {
  const applyFormat = (formatFn: () => string) => {
    if (!textareaRef.current) return
    const newContent = formatFn()
    onContentChange(newContent)
  }

  const formatBold = () => {
    if (!textareaRef.current) return ''
    return wrapSelectedText(textareaRef.current, '**')
  }

  const formatItalic = () => {
    if (!textareaRef.current) return ''
    return wrapSelectedText(textareaRef.current, '*')
  }

  const formatCode = () => {
    if (!textareaRef.current) return ''
    return wrapSelectedText(textareaRef.current, '`')
  }

  const formatHeading1 = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '# ')
  }

  const formatHeading2 = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '## ')
  }

  const formatHeading3 = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '### ')
  }

  const formatBulletList = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '- ')
  }

  const formatNumberedList = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '1. ')
  }

  const formatQuote = () => {
    if (!textareaRef.current) return ''
    return insertLinePrefix(textareaRef.current, '> ')
  }

  const insertLink = () => {
    if (!textareaRef.current) return ''
    return insertTextAtCursor(textareaRef.current, '[Link Text](https://example.com)')
  }

  const insertImage = () => {
    if (!textareaRef.current) return ''
    return insertTextAtCursor(textareaRef.current, '![Alt Text](image-url)')
  }

  const insertHorizontalRule = () => {
    if (!textareaRef.current) return ''
    return insertTextAtCursor(textareaRef.current, '\n---\n')
  }

  return (
    <div className="flex items-center gap-1 p-2 border-b bg-muted/30">
      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatHeading1)}
          title="Heading 1 (Ctrl+1)"
        >
          <Heading1 className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatHeading2)}
          title="Heading 2 (Ctrl+2)"
        >
          <Heading2 className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatHeading3)}
          title="Heading 3 (Ctrl+3)"
        >
          <Heading3 className="w-4 h-4" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatBold)}
          title="Bold (Ctrl+B)"
        >
          <Bold className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatItalic)}
          title="Italic (Ctrl+I)"
        >
          <Italic className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatCode)}
          title="Code (Ctrl+`)"
        >
          <Code className="w-4 h-4" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatBulletList)}
          title="Bullet List (Ctrl+Shift+8)"
        >
          <List className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatNumberedList)}
          title="Numbered List (Ctrl+Shift+7)"
        >
          <ListOrdered className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(formatQuote)}
          title="Quote (Ctrl+Shift+.)"
        >
          <Quote className="w-4 h-4" />
        </Button>
      </div>

      <div className="w-px h-6 bg-border mx-1" />

      <div className="flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(insertLink)}
          title="Insert Link (Ctrl+K)"
        >
          <Link className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(insertImage)}
          title="Insert Image"
        >
          <Image className="w-4 h-4" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => applyFormat(insertHorizontalRule)}
          title="Horizontal Rule"
        >
          <Minus className="w-4 h-4" />
        </Button>
      </div>
    </div>
  )
}
