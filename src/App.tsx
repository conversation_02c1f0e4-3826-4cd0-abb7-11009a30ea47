import { useState, useEffect } from 'react'
import { VaultSelector } from './components/VaultSelector'
import { MainLayout } from './components/MainLayout'
import { ThemeProvider } from './components/ThemeProvider'
import { Vault } from './types'
import { vaultStorage } from './lib/vaultStorage'
import './index.css'

function App() {
  const [currentVault, setCurrentVault] = useState<Vault | null>(null)
  const [vaults, setVaults] = useState<Vault[]>([])

  // Load saved vaults from IndexedDB on app start
  useEffect(() => {
    const loadVaults = async () => {
      try {
        await vaultStorage.init()
        await vaultStorage.migrateFromLocalStorage() // Migrate from localStorage if needed
        const savedVaults = await vaultStorage.getAllVaults()
        setVaults(savedVaults)
      } catch (error) {
        console.error('Error loading saved vaults:', error)
      }
    }
    loadVaults()
  }, [])

  const handleVaultSelect = async (vault: Vault) => {
    setCurrentVault(vault)

    // Update last opened timestamp
    const updatedVault = { ...vault, lastOpened: new Date() }
    const updatedVaults = vaults.map(v =>
      v.id === vault.id ? updatedVault : v
    )
    setVaults(updatedVaults)

    // Save to IndexedDB
    try {
      await vaultStorage.updateVault(updatedVault)
    } catch (error) {
      console.error('Error updating vault:', error)
    }
  }

  const handleVaultCreate = async (vault: Vault) => {
    setVaults(prev => [...prev, vault])
    setCurrentVault(vault)

    // Save to IndexedDB
    try {
      await vaultStorage.saveVault(vault)
    } catch (error) {
      console.error('Error saving vault:', error)
    }
  }

  const handleVaultClose = () => {
    setCurrentVault(null)
  }

  return (
    <ThemeProvider defaultTheme="system" storageKey="kapture-theme">
      <div className="min-h-screen bg-background text-foreground">
        {!currentVault ? (
          <VaultSelector
            vaults={vaults}
            onVaultSelect={handleVaultSelect}
            onVaultCreate={handleVaultCreate}
          />
        ) : (
          <MainLayout
            vault={currentVault}
            vaults={vaults}
            onVaultClose={handleVaultClose}
            onVaultSwitch={handleVaultSelect}
            onVaultCreate={handleVaultCreate}
          />
        )}
      </div>
    </ThemeProvider>
  )
}

export default App
