import { Vault } from '../types'

const DB_NAME = 'KaptureHQ'
const DB_VERSION = 1
const VAULT_STORE = 'vaults'

interface VaultDB extends IDBDatabase {}

class VaultStorage {
  private db: VaultDB | null = null

  async init(): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION)

      request.onerror = () => {
        reject(new Error('Failed to open IndexedDB'))
      }

      request.onsuccess = () => {
        this.db = request.result
        resolve()
      }

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result
        
        // Create vaults store if it doesn't exist
        if (!db.objectStoreNames.contains(VAULT_STORE)) {
          const vaultStore = db.createObjectStore(VAULT_STORE, { keyPath: 'id' })
          vaultStore.createIndex('lastOpened', 'lastOpened', { unique: false })
        }
      }
    })
  }

  async saveVault(vault: Vault): Promise<void> {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([VAULT_STORE], 'readwrite')
      const store = transaction.objectStore(VAULT_STORE)
      
      const request = store.put({
        ...vault,
        lastOpened: vault.lastOpened.toISOString()
      })

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to save vault'))
    })
  }

  async getAllVaults(): Promise<Vault[]> {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([VAULT_STORE], 'readonly')
      const store = transaction.objectStore(VAULT_STORE)
      const request = store.getAll()

      request.onsuccess = () => {
        const vaults = request.result.map((vault: any) => ({
          ...vault,
          lastOpened: new Date(vault.lastOpened)
        }))
        resolve(vaults)
      }

      request.onerror = () => reject(new Error('Failed to load vaults'))
    })
  }

  async updateVault(vault: Vault): Promise<void> {
    return this.saveVault(vault)
  }

  async deleteVault(vaultId: string): Promise<void> {
    if (!this.db) await this.init()
    
    return new Promise((resolve, reject) => {
      const transaction = this.db!.transaction([VAULT_STORE], 'readwrite')
      const store = transaction.objectStore(VAULT_STORE)
      const request = store.delete(vaultId)

      request.onsuccess = () => resolve()
      request.onerror = () => reject(new Error('Failed to delete vault'))
    })
  }

  async migrateFromLocalStorage(): Promise<void> {
    try {
      const savedVaults = localStorage.getItem('kapture-vaults')
      if (savedVaults) {
        const vaults: Vault[] = JSON.parse(savedVaults).map((vault: any) => ({
          ...vault,
          lastOpened: new Date(vault.lastOpened)
        }))

        // Save each vault to IndexedDB
        for (const vault of vaults) {
          await this.saveVault(vault)
        }

        // Remove from localStorage after successful migration
        localStorage.removeItem('kapture-vaults')
        console.log('Successfully migrated vaults from localStorage to IndexedDB')
      }
    } catch (error) {
      console.error('Error migrating vaults from localStorage:', error)
    }
  }
}

export const vaultStorage = new VaultStorage()
