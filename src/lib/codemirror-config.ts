import { EditorState, Extension, StateField, StateEffect } from '@codemirror/state'
import { EditorView, keymap, highlightActiveLine, lineNumbers, highlightActiveLineGutter, Decoration, DecorationSet, WidgetType } from '@codemirror/view'
import { defaultKeymap, history, historyKeymap, indentWithTab } from '@codemirror/commands'
import { searchKeymap, highlightSelectionMatches } from '@codemirror/search'
import { autocompletion, completionKeymap, closeBrackets, closeBracketsKeymap } from '@codemirror/autocomplete'
import { foldGutter, indentOnInput, bracketMatching, foldKeymap, syntaxHighlighting, HighlightStyle } from '@codemirror/language'
import { markdown } from '@codemirror/lang-markdown'
import { oneDark } from '@codemirror/theme-one-dark'
import { tags } from '@lezer/highlight'

// Hybrid markdown rendering widgets
class HeaderWidget extends WidgetType {
  constructor(private level: number, private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement(`h${this.level}`)
    element.textContent = this.text
    element.className = `cm-header cm-header${this.level}`
    element.style.cssText = `
      margin: 0;
      font-weight: ${this.level <= 2 ? 'bold' : '600'};
      font-size: ${this.level === 1 ? '2em' : this.level === 2 ? '1.5em' : '1.25em'};
      color: var(--foreground);
      line-height: 1.2;
    `
    return element
  }
}

class StrongWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('strong')
    element.textContent = this.text
    element.style.cssText = 'font-weight: 600; color: var(--foreground);'
    return element
  }
}

class EmphasisWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('em')
    element.textContent = this.text
    element.style.cssText = 'font-style: italic; color: var(--foreground);'
    return element
  }
}

class CodeWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('code')
    element.textContent = this.text
    element.style.cssText = `
      background-color: var(--muted);
      color: var(--foreground);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.85em;
    `
    return element
  }
}

// Hybrid rendering extension
const hybridMarkdownExtension = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes)

    if (tr.docChanged) {
      const doc = tr.state.doc
      const newDecorations: any[] = []

      for (let i = 1; i <= doc.lines; i++) {
        const line = doc.line(i)
        const text = line.text

        // Headers
        const headerMatch = text.match(/^(#{1,6})\s+(.+)$/)
        if (headerMatch) {
          const level = headerMatch[1].length
          const headerText = headerMatch[2]
          newDecorations.push(
            Decoration.replace({
              widget: new HeaderWidget(level, headerText),
              inclusive: true
            }).range(line.from, line.to)
          )
        }

        // Bold text
        let boldMatch
        const boldRegex = /\*\*([^*]+)\*\*/g
        while ((boldMatch = boldRegex.exec(text)) !== null) {
          newDecorations.push(
            Decoration.replace({
              widget: new StrongWidget(boldMatch[1]),
              inclusive: false
            }).range(line.from + boldMatch.index, line.from + boldMatch.index + boldMatch[0].length)
          )
        }

        // Italic text
        let italicMatch
        const italicRegex = /\*([^*]+)\*/g
        while ((italicMatch = italicRegex.exec(text)) !== null) {
          // Skip if it's part of bold text
          if (text[italicMatch.index - 1] !== '*' && text[italicMatch.index + italicMatch[0].length] !== '*') {
            newDecorations.push(
              Decoration.replace({
                widget: new EmphasisWidget(italicMatch[1]),
                inclusive: false
              }).range(line.from + italicMatch.index, line.from + italicMatch.index + italicMatch[0].length)
            )
          }
        }

        // Inline code
        let codeMatch
        const codeRegex = /`([^`]+)`/g
        while ((codeMatch = codeRegex.exec(text)) !== null) {
          newDecorations.push(
            Decoration.replace({
              widget: new CodeWidget(codeMatch[1]),
              inclusive: false
            }).range(line.from + codeMatch.index, line.from + codeMatch.index + codeMatch[0].length)
          )
        }
      }

      decorations = Decoration.set(newDecorations)
    }

    return decorations
  },
  provide: f => EditorView.decorations.from(f)
})

// Markdown syntax highlighting
const markdownHighlighting = syntaxHighlighting(HighlightStyle.define([
  { tag: tags.heading1, fontSize: '2em', fontWeight: 'bold' },
  { tag: tags.heading2, fontSize: '1.5em', fontWeight: 'bold' },
  { tag: tags.heading3, fontSize: '1.25em', fontWeight: 'bold' },
  { tag: tags.strong, fontWeight: '600' },
  { tag: tags.emphasis, fontStyle: 'italic' },
  { tag: tags.monospace, fontFamily: 'monospace', backgroundColor: 'var(--muted)' },
  { tag: tags.link, color: 'var(--primary)', textDecoration: 'underline' },
  { tag: tags.quote, color: 'var(--muted-foreground)', fontStyle: 'italic' },
]))

// Obsidian-style theme configuration matching the exact structure from Ref.html
const obsidianTheme = EditorView.theme({
  // Base editor styling (matches .ͼ1 class)
  '&': {
    position: 'relative !important',
    boxSizing: 'border-box',
    display: 'flex !important',
    flexDirection: 'column',
    color: 'var(--foreground)',
    backgroundColor: 'var(--background)',
    fontSize: '16px',
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif',
    lineHeight: '1.4',
  },

  // Focus outline (matches .ͼ1.cm-focused)
  '&.cm-focused': {
    outline: '1px dotted #212121',
  },

  // Scroller styling (matches .ͼ1 .cm-scroller)
  '.cm-scroller': {
    display: 'flex !important',
    alignItems: 'flex-start !important',
    fontFamily: 'monospace',
    lineHeight: '1.4',
    height: '100%',
    overflowX: 'auto',
    position: 'relative',
    zIndex: '0',
    overflowAnchor: 'none',
  },

  // Content styling (matches .ͼ1 .cm-content)
  '.cm-content': {
    margin: '0',
    flexGrow: '2',
    flexShrink: '0',
    display: 'block',
    whiteSpace: 'pre',
    wordWrap: 'normal',
    boxSizing: 'border-box',
    minHeight: '100%',
    padding: '4px 0',
    outline: 'none',
    caretColor: 'var(--foreground)',
  },

  // Content editable styling (matches .ͼ1 .cm-content[contenteditable=true])
  '.cm-content[contenteditable=true]': {
    WebkitUserModify: 'read-write-plaintext-only',
  },

  // Line wrapping (matches .ͼ1 .cm-lineWrapping)
  '.cm-lineWrapping': {
    whiteSpace: 'pre-wrap',
    wordBreak: 'break-word',
    overflowWrap: 'anywhere',
    flexShrink: '1',
  },

  // Focus content styling
  '&.cm-focused .cm-content': {
    outline: 'none',
  },

  // Line styling (matches .ͼ1 .cm-line)
  '.cm-line': {
    display: 'block',
    padding: '0 2px 0 6px',
    lineHeight: '1.4',
  },

  // Layer styling (matches .ͼ1 .cm-layer)
  '.cm-layer': {
    position: 'absolute',
    left: '0',
    top: '0',
    contain: 'size style',
  },

  '.cm-layer > *': {
    position: 'absolute',
  },

  // Selection background styling (matches .ͼ2/.ͼ3 .cm-selectionBackground)
  '.cm-selectionBackground': {
    backgroundColor: 'var(--accent)',
  },

  // Focused selection background
  '&.cm-focused > .cm-scroller > .cm-selectionLayer .cm-selectionBackground': {
    backgroundColor: 'var(--accent)',
  },

  // Cursor layer styling (matches .ͼ1 .cm-cursorLayer)
  '.cm-cursorLayer': {
    pointerEvents: 'none',
  },

  // Cursor blinking animation (matches .ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer)
  '&.cm-focused > .cm-scroller > .cm-cursorLayer': {
    animation: 'steps(1) cm-blink 1.2s infinite',
  },

  // Cursor styling (matches .ͼ1 .cm-cursor, .ͼ1 .cm-dropCursor)
  '.cm-cursor, .cm-dropCursor': {
    borderLeft: '1.2px solid var(--foreground)',
    marginLeft: '-0.6px',
    pointerEvents: 'none',
  },

  '.cm-cursor': {
    display: 'none',
  },

  '.cm-dropCursor': {
    position: 'absolute',
  },

  // Show cursor when focused (matches .ͼ1.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor)
  '&.cm-focused > .cm-scroller > .cm-cursorLayer .cm-cursor': {
    display: 'block',
  },

  // Active line highlighting (matches .ͼ2/.ͼ3 .cm-activeLine)
  '.cm-activeLine': {
    backgroundColor: 'var(--accent)',
  },

  // Special character styling (matches .ͼ2/.ͼ3 .cm-specialChar)
  '.cm-specialChar': {
    color: 'var(--destructive)',
  },

  // Gutters styling (matches .ͼ1 .cm-gutters)
  '.cm-gutters': {
    flexShrink: '0',
    display: 'flex',
    height: '100%',
    boxSizing: 'border-box',
    insetInlineStart: '0',
    zIndex: '200',
    backgroundColor: 'var(--muted)',
    color: 'var(--muted-foreground)',
    borderRight: '1px solid var(--border)',
  },

  // Individual gutter styling (matches .ͼ1 .cm-gutter)
  '.cm-gutter': {
    display: 'flex !important',
    flexDirection: 'column',
    flexShrink: '0',
    boxSizing: 'border-box',
    minHeight: '100%',
    overflow: 'hidden',
  },

  // Gutter element styling (matches .ͼ1 .cm-gutterElement)
  '.cm-gutterElement': {
    boxSizing: 'border-box',
  },

  // Line numbers styling (matches .ͼ1 .cm-lineNumbers .cm-gutterElement)
  '.cm-lineNumbers .cm-gutterElement': {
    padding: '0 3px 0 5px',
    minWidth: '20px',
    textAlign: 'right',
    whiteSpace: 'nowrap',
  },

  // Active line gutter (matches .ͼ2/.ͼ3 .cm-activeLineGutter)
  '.cm-activeLineGutter': {
    backgroundColor: 'var(--accent)',
  },

  // Panels styling (matches .ͼ1 .cm-panels)
  '.cm-panels': {
    boxSizing: 'border-box',
    position: 'sticky',
    left: '0',
    right: '0',
    zIndex: '300',
    backgroundColor: 'var(--muted)',
    color: 'var(--foreground)',
  },

  '.cm-panels-top': {
    borderBottom: '1px solid var(--border)',
  },

  '.cm-panels-bottom': {
    borderTop: '1px solid var(--border)',
  },

  // Tab styling (matches .ͼ1 .cm-tab)
  '.cm-tab': {
    display: 'inline-block',
    overflow: 'hidden',
    verticalAlign: 'bottom',
  },

  // Widget buffer styling (matches .ͼ1 .cm-widgetBuffer)
  '.cm-widgetBuffer': {
    verticalAlign: 'text-top',
    height: '1em',
    width: '0',
    display: 'inline',
  },

  // Placeholder styling (matches .ͼ1 .cm-placeholder)
  '.cm-placeholder': {
    color: 'var(--muted-foreground)',
    display: 'inline-block',
    verticalAlign: 'top',
  },

  // Markdown-specific styling
  '.cm-header': {
    fontWeight: 'bold',
    color: 'var(--foreground)',
  },
  '.cm-header1': {
    fontSize: '2em',
    fontWeight: '700',
    color: 'var(--foreground)',
  },
  '.cm-header2': {
    fontSize: '1.5em',
    fontWeight: '600',
    color: 'var(--foreground)',
  },
  '.cm-header3': {
    fontSize: '1.25em',
    fontWeight: '600',
    color: 'var(--foreground)',
  },
  '.cm-strong': {
    fontWeight: 'bold',
    color: 'var(--foreground)',
  },
  '.cm-emphasis': {
    fontStyle: 'italic',
    color: 'var(--foreground)',
  },
  '.cm-strikethrough': {
    textDecoration: 'line-through',
    color: 'var(--muted-foreground)',
  },
  '.cm-code': {
    backgroundColor: 'var(--muted)',
    color: 'var(--foreground)',
    padding: '2px 4px',
    borderRadius: '3px',
    fontFamily: '"SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace',
    fontSize: '0.85em',
  },
  '.cm-link': {
    color: 'var(--primary)',
    textDecoration: 'underline',
  },
  '.cm-quote': {
    color: 'var(--muted-foreground)',
    fontStyle: 'italic',
    borderLeft: '4px solid var(--border)',
    paddingLeft: '1em',
    marginLeft: '0',
  },
  '.cm-list': {
    color: 'var(--foreground)',
  },
  '.cm-url': {
    color: 'var(--primary)',
    textDecoration: 'underline',
  },
  '.cm-meta': {
    color: 'var(--muted-foreground)',
  },
  '.cm-keyword': {
    color: 'var(--primary)',
    fontWeight: 'bold',
  },
  '.cm-atom': {
    color: 'var(--primary)',
  },
  '.cm-number': {
    color: 'var(--primary)',
  },
  '.cm-def': {
    color: 'var(--foreground)',
    fontWeight: 'bold',
  },
  '.cm-variable': {
    color: 'var(--foreground)',
  },
  '.cm-variable-2': {
    color: 'var(--foreground)',
  },
  '.cm-variable-3': {
    color: 'var(--foreground)',
  },
  '.cm-property': {
    color: 'var(--foreground)',
  },
  '.cm-operator': {
    color: 'var(--foreground)',
  },
  '.cm-comment': {
    color: 'var(--muted-foreground)',
    fontStyle: 'italic',
  },
  '.cm-string': {
    color: 'var(--primary)',
  },
  '.cm-string-2': {
    color: 'var(--primary)',
  },
  '.cm-qualifier': {
    color: 'var(--muted-foreground)',
  },
  '.cm-builtin': {
    color: 'var(--primary)',
  },
  '.cm-bracket': {
    color: 'var(--foreground)',
  },
  '.cm-tag': {
    color: 'var(--primary)',
  },
  '.cm-attribute': {
    color: 'var(--foreground)',
  },
  '.cm-hr': {
    color: 'var(--border)',
  },
})

// Custom keymap for Obsidian-like shortcuts
const obsidianKeymap = keymap.of([
  // Basic formatting
  { key: 'Mod-b', run: () => true }, // Bold - handled by parent component
  { key: 'Mod-i', run: () => true }, // Italic - handled by parent component
  { key: 'Mod-k', run: () => true }, // Link - handled by parent component
  { key: 'Mod-e', run: () => true }, // Code - handled by parent component

  // Headers
  { key: 'Mod-1', run: () => true }, // H1 - handled by parent component
  { key: 'Mod-2', run: () => true }, // H2 - handled by parent component
  { key: 'Mod-3', run: () => true }, // H3 - handled by parent component

  // Lists
  { key: 'Mod-Shift-8', run: () => true }, // Bullet list - handled by parent component
  { key: 'Mod-Shift-7', run: () => true }, // Numbered list - handled by parent component

  // Save
  { key: 'Mod-s', run: () => true }, // Save - handled by parent component
])

// Basic extensions for markdown editing
export const basicExtensions: Extension[] = [
  // Language support
  markdown(),

  // History
  history(),

  // Basic editing features
  indentOnInput(),
  bracketMatching(),
  closeBrackets(),
  autocompletion(),
  highlightSelectionMatches(),

  // Search
  searchKeymap,

  // Syntax highlighting
  markdownHighlighting,

  // Keymaps
  keymap.of([
    ...defaultKeymap,
    ...historyKeymap,
    ...foldKeymap,
    ...completionKeymap,
    ...closeBracketsKeymap,
    indentWithTab,
  ]),

  // Custom keymap
  obsidianKeymap,

  // Theme
  obsidianTheme,
]

// Hybrid extensions with live preview
export const hybridExtensions: Extension[] = [
  ...basicExtensions,
  hybridMarkdownExtension,
]

// Extensions with line numbers (optional)
export const extendedExtensions: Extension[] = [
  ...basicExtensions,
  lineNumbers(),
  foldGutter(),
  highlightActiveLine(),
  highlightActiveLineGutter(),
]

// Dark theme extensions
export const darkThemeExtensions: Extension[] = [
  ...basicExtensions,
  oneDark,
]

// Create editor state
export function createEditorState(doc: string, extensions: Extension[] = basicExtensions): EditorState {
  return EditorState.create({
    doc,
    extensions,
  })
}

// Create editor view
export function createEditorView(
  state: EditorState,
  parent: Element,
  onUpdate?: (update: any) => void
): EditorView {
  const view = new EditorView({
    state,
    parent,
    dispatch: (transaction) => {
      view.update([transaction])
      if (onUpdate && transaction.docChanged) {
        onUpdate(transaction)
      }
    },
  })
  return view
}
