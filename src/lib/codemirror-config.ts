import { EditorState, Extension, StateField, StateEffect } from '@codemirror/state'
import { EditorView, keymap, highlightActiveLine, lineNumbers, highlightActiveLineGutter, Decoration, DecorationSet, WidgetType } from '@codemirror/view'
import { defaultKeymap, history, historyKeymap, indentWithTab } from '@codemirror/commands'
import { searchKeymap, highlightSelectionMatches } from '@codemirror/search'
import { autocompletion, completionKeymap, closeBrackets, closeBracketsKeymap } from '@codemirror/autocomplete'
import { foldGutter, indentOnInput, bracketMatching, foldKeymap, syntaxHighlighting, HighlightStyle } from '@codemirror/language'
import { markdown } from '@codemirror/lang-markdown'
import { oneDark } from '@codemirror/theme-one-dark'
import { tags } from '@lezer/highlight'

// Hybrid markdown rendering widgets
class HeaderWidget extends WidgetType {
  constructor(private level: number, private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement(`h${this.level}`)
    element.textContent = this.text
    element.className = `cm-header cm-header${this.level}`
    element.style.cssText = `
      margin: 0;
      font-weight: ${this.level <= 2 ? 'bold' : '600'};
      font-size: ${this.level === 1 ? '2em' : this.level === 2 ? '1.5em' : '1.25em'};
      color: var(--foreground);
      line-height: 1.2;
    `
    return element
  }
}

class StrongWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('strong')
    element.textContent = this.text
    element.style.cssText = 'font-weight: 600; color: var(--foreground);'
    return element
  }
}

class EmphasisWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('em')
    element.textContent = this.text
    element.style.cssText = 'font-style: italic; color: var(--foreground);'
    return element
  }
}

class CodeWidget extends WidgetType {
  constructor(private text: string) {
    super()
  }

  toDOM() {
    const element = document.createElement('code')
    element.textContent = this.text
    element.style.cssText = `
      background-color: var(--muted);
      color: var(--foreground);
      padding: 2px 4px;
      border-radius: 3px;
      font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
      font-size: 0.85em;
    `
    return element
  }
}

// Hybrid rendering extension
const hybridMarkdownExtension = StateField.define<DecorationSet>({
  create() {
    return Decoration.none
  },
  update(decorations, tr) {
    decorations = decorations.map(tr.changes)

    if (tr.docChanged) {
      const doc = tr.state.doc
      const newDecorations: any[] = []

      for (let i = 1; i <= doc.lines; i++) {
        const line = doc.line(i)
        const text = line.text

        // Headers
        const headerMatch = text.match(/^(#{1,6})\s+(.+)$/)
        if (headerMatch) {
          const level = headerMatch[1].length
          const headerText = headerMatch[2]
          newDecorations.push(
            Decoration.replace({
              widget: new HeaderWidget(level, headerText),
              inclusive: true
            }).range(line.from, line.to)
          )
        }

        // Bold text
        let boldMatch
        const boldRegex = /\*\*([^*]+)\*\*/g
        while ((boldMatch = boldRegex.exec(text)) !== null) {
          newDecorations.push(
            Decoration.replace({
              widget: new StrongWidget(boldMatch[1]),
              inclusive: false
            }).range(line.from + boldMatch.index, line.from + boldMatch.index + boldMatch[0].length)
          )
        }

        // Italic text
        let italicMatch
        const italicRegex = /\*([^*]+)\*/g
        while ((italicMatch = italicRegex.exec(text)) !== null) {
          // Skip if it's part of bold text
          if (text[italicMatch.index - 1] !== '*' && text[italicMatch.index + italicMatch[0].length] !== '*') {
            newDecorations.push(
              Decoration.replace({
                widget: new EmphasisWidget(italicMatch[1]),
                inclusive: false
              }).range(line.from + italicMatch.index, line.from + italicMatch.index + italicMatch[0].length)
            )
          }
        }

        // Inline code
        let codeMatch
        const codeRegex = /`([^`]+)`/g
        while ((codeMatch = codeRegex.exec(text)) !== null) {
          newDecorations.push(
            Decoration.replace({
              widget: new CodeWidget(codeMatch[1]),
              inclusive: false
            }).range(line.from + codeMatch.index, line.from + codeMatch.index + codeMatch[0].length)
          )
        }
      }

      decorations = Decoration.set(newDecorations)
    }

    return decorations
  },
  provide: f => EditorView.decorations.from(f)
})

// Markdown syntax highlighting
const markdownHighlighting = syntaxHighlighting(HighlightStyle.define([
  { tag: tags.heading1, fontSize: '2em', fontWeight: 'bold' },
  { tag: tags.heading2, fontSize: '1.5em', fontWeight: 'bold' },
  { tag: tags.heading3, fontSize: '1.25em', fontWeight: 'bold' },
  { tag: tags.strong, fontWeight: '600' },
  { tag: tags.emphasis, fontStyle: 'italic' },
  { tag: tags.monospace, fontFamily: 'monospace', backgroundColor: 'var(--muted)' },
  { tag: tags.link, color: 'var(--primary)', textDecoration: 'underline' },
  { tag: tags.quote, color: 'var(--muted-foreground)', fontStyle: 'italic' },
]))

// Obsidian-style theme configuration
const obsidianTheme = EditorView.theme({
  '&': {
    color: 'var(--foreground)',
    backgroundColor: 'var(--background)',
    fontSize: '16px',
    fontFamily: '"Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    lineHeight: '1.6',
  },
  '.cm-content': {
    padding: '0',
    caretColor: 'var(--foreground)',
    minHeight: '600px',
  },
  '.cm-focused .cm-content': {
    outline: 'none',
  },
  '.cm-editor': {
    fontSize: '16px',
  },
  '.cm-scroller': {
    fontFamily: 'inherit',
  },
  '.cm-line': {
    padding: '0 0',
    lineHeight: '1.6',
  },
  // Markdown-specific styling
  '.cm-header': {
    fontWeight: 'bold',
  },
  '.cm-header1': {
    fontSize: '2em',
    color: 'var(--foreground)',
  },
  '.cm-header2': {
    fontSize: '1.5em',
    color: 'var(--foreground)',
  },
  '.cm-header3': {
    fontSize: '1.25em',
    color: 'var(--foreground)',
  },
  '.cm-strong': {
    fontWeight: 'bold',
  },
  '.cm-emphasis': {
    fontStyle: 'italic',
  },
  '.cm-strikethrough': {
    textDecoration: 'line-through',
  },
  '.cm-code': {
    backgroundColor: 'var(--muted)',
    padding: '2px 4px',
    borderRadius: '3px',
    fontFamily: '"JetBrains Mono", "Fira Code", Consolas, monospace',
  },
  '.cm-link': {
    color: 'var(--primary)',
    textDecoration: 'underline',
  },
  '.cm-quote': {
    color: 'var(--muted-foreground)',
    fontStyle: 'italic',
  },
  '.cm-list': {
    color: 'var(--foreground)',
  },
  // Selection and cursor
  '.cm-selectionBackground, ::selection': {
    backgroundColor: 'var(--accent)',
  },
  '.cm-focused .cm-selectionBackground': {
    backgroundColor: 'var(--accent)',
  },
  '.cm-activeLine': {
    backgroundColor: 'transparent',
  },
  '.cm-activeLineGutter': {
    backgroundColor: 'transparent',
  },
  // Search highlighting
  '.cm-searchMatch': {
    backgroundColor: 'var(--accent)',
    outline: '1px solid var(--ring)',
  },
  '.cm-searchMatch.cm-searchMatch-selected': {
    backgroundColor: 'var(--accent)',
  },
  // Gutters
  '.cm-gutters': {
    backgroundColor: 'var(--background)',
    color: 'var(--muted-foreground)',
    border: 'none',
  },
  '.cm-lineNumbers .cm-gutterElement': {
    padding: '0 8px 0 16px',
  },
  // Fold markers
  '.cm-foldPlaceholder': {
    backgroundColor: 'var(--muted)',
    border: '1px solid var(--border)',
    color: 'var(--muted-foreground)',
  },
})

// Custom keymap for Obsidian-like shortcuts
const obsidianKeymap = keymap.of([
  // Basic formatting
  { key: 'Mod-b', run: () => true }, // Bold - handled by parent component
  { key: 'Mod-i', run: () => true }, // Italic - handled by parent component
  { key: 'Mod-k', run: () => true }, // Link - handled by parent component
  { key: 'Mod-e', run: () => true }, // Code - handled by parent component

  // Headers
  { key: 'Mod-1', run: () => true }, // H1 - handled by parent component
  { key: 'Mod-2', run: () => true }, // H2 - handled by parent component
  { key: 'Mod-3', run: () => true }, // H3 - handled by parent component

  // Lists
  { key: 'Mod-Shift-8', run: () => true }, // Bullet list - handled by parent component
  { key: 'Mod-Shift-7', run: () => true }, // Numbered list - handled by parent component

  // Save
  { key: 'Mod-s', run: () => true }, // Save - handled by parent component
])

// Basic extensions for markdown editing
export const basicExtensions: Extension[] = [
  // Language support
  markdown(),

  // History
  history(),

  // Basic editing features
  indentOnInput(),
  bracketMatching(),
  closeBrackets(),
  autocompletion(),
  highlightSelectionMatches(),

  // Search
  searchKeymap,

  // Syntax highlighting
  markdownHighlighting,

  // Keymaps
  keymap.of([
    ...defaultKeymap,
    ...historyKeymap,
    ...foldKeymap,
    ...completionKeymap,
    ...closeBracketsKeymap,
    indentWithTab,
  ]),

  // Custom keymap
  obsidianKeymap,

  // Theme
  obsidianTheme,
]

// Hybrid extensions with live preview
export const hybridExtensions: Extension[] = [
  ...basicExtensions,
  hybridMarkdownExtension,
]

// Extensions with line numbers (optional)
export const extendedExtensions: Extension[] = [
  ...basicExtensions,
  lineNumbers(),
  foldGutter(),
  highlightActiveLine(),
  highlightActiveLineGutter(),
]

// Dark theme extensions
export const darkThemeExtensions: Extension[] = [
  ...basicExtensions,
  oneDark,
]

// Create editor state
export function createEditorState(doc: string, extensions: Extension[] = basicExtensions): EditorState {
  return EditorState.create({
    doc,
    extensions,
  })
}

// Create editor view
export function createEditorView(
  state: EditorState,
  parent: Element,
  onUpdate?: (update: any) => void
): EditorView {
  const view = new EditorView({
    state,
    parent,
    dispatch: (transaction) => {
      view.update([transaction])
      if (onUpdate && transaction.docChanged) {
        onUpdate(transaction)
      }
    },
  })
  return view
}
