import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

export function formatDateTime(date: Date | string): string {
  const d = typeof date === 'string' ? new Date(date) : date
  return d.toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

export function generateId(): string {
  return Math.random().toString(36).substr(2, 9)
}

export function sanitizeFileName(name: string): string {
  return name.replace(/[^a-z0-9]/gi, '_').toLowerCase()
}

export function getFileExtension(filename: string): string {
  return filename.split('.').pop()?.toLowerCase() || ''
}

export function isMarkdownFile(filename: string): boolean {
  return getFileExtension(filename) === 'md'
}

export function isCsvFile(filename: string): boolean {
  return getFileExtension(filename) === 'csv'
}

export function getDisplayName(filename: string): string {
  // Remove file extension for display purposes
  const lastDotIndex = filename.lastIndexOf('.')
  if (lastDotIndex > 0) {
    return filename.substring(0, lastDotIndex)
  }
  return filename
}

export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

// CSV parsing utilities
export function parseCSV(content: string): string[][] {
  if (!content.trim()) return []

  const lines = content.split('\n').filter(line => line.trim())
  const result: string[][] = []

  for (const line of lines) {
    const row: string[] = []
    let current = ''
    let inQuotes = false

    for (let i = 0; i < line.length; i++) {
      const char = line[i]

      if (char === '"') {
        inQuotes = !inQuotes
      } else if (char === ',' && !inQuotes) {
        row.push(current.trim())
        current = ''
      } else {
        current += char
      }
    }

    row.push(current.trim())
    result.push(row)
  }

  return result
}

export function stringifyCSV(data: string[][]): string {
  return data.map(row =>
    row.map(cell => {
      // Escape quotes and wrap in quotes if contains comma or quote
      if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
        return `"${cell.replace(/"/g, '""')}"`
      }
      return cell
    }).join(',')
  ).join('\n')
}

// Markdown formatting utilities
export interface TextSelection {
  start: number
  end: number
  text: string
}

export function getTextSelection(textarea: HTMLTextAreaElement): TextSelection {
  return {
    start: textarea.selectionStart,
    end: textarea.selectionEnd,
    text: textarea.value.substring(textarea.selectionStart, textarea.selectionEnd)
  }
}

export function insertTextAtCursor(textarea: HTMLTextAreaElement, text: string): string {
  const start = textarea.selectionStart
  const end = textarea.selectionEnd
  const value = textarea.value

  const newValue = value.substring(0, start) + text + value.substring(end)

  // Set cursor position after inserted text
  setTimeout(() => {
    textarea.selectionStart = textarea.selectionEnd = start + text.length
    textarea.focus()
  }, 0)

  return newValue
}

export function wrapSelectedText(textarea: HTMLTextAreaElement, wrapper: string, endWrapper?: string): string {
  const selection = getTextSelection(textarea)
  const end = endWrapper || wrapper
  const newText = wrapper + selection.text + end

  const newValue = textarea.value.substring(0, selection.start) + newText + textarea.value.substring(selection.end)

  // Set cursor position
  setTimeout(() => {
    if (selection.text) {
      // If text was selected, select the wrapped text
      textarea.selectionStart = selection.start + wrapper.length
      textarea.selectionEnd = selection.start + wrapper.length + selection.text.length
    } else {
      // If no text selected, place cursor between wrappers
      textarea.selectionStart = textarea.selectionEnd = selection.start + wrapper.length
    }
    textarea.focus()
  }, 0)

  return newValue
}

export function insertLinePrefix(textarea: HTMLTextAreaElement, prefix: string): string {
  const selection = getTextSelection(textarea)
  const lines = textarea.value.split('\n')
  const startLine = textarea.value.substring(0, selection.start).split('\n').length - 1
  const endLine = textarea.value.substring(0, selection.end).split('\n').length - 1

  // Toggle prefix for selected lines
  let allHavePrefix = true
  for (let i = startLine; i <= endLine; i++) {
    if (!lines[i].startsWith(prefix)) {
      allHavePrefix = false
      break
    }
  }

  if (allHavePrefix) {
    // Remove prefix
    for (let i = startLine; i <= endLine; i++) {
      lines[i] = lines[i].substring(prefix.length)
    }
  } else {
    // Add prefix
    for (let i = startLine; i <= endLine; i++) {
      lines[i] = prefix + lines[i]
    }
  }

  const newValue = lines.join('\n')

  // Restore selection
  setTimeout(() => {
    textarea.selectionStart = selection.start
    textarea.selectionEnd = selection.end
    textarea.focus()
  }, 0)

  return newValue
}
