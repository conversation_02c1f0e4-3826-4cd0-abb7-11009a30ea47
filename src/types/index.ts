// Vault and file system types
export interface Vault {
  id: string
  name: string
  path: string
  lastOpened: Date
}

export interface FileSystemItem {
  name: string
  path: string
  isDirectory: boolean
  children?: FileSystemItem[]
}

// Database types
export type ColumnType = 'text' | 'number' | 'date' | 'checkbox' | 'select' | 'reference'

export interface ColumnDefinition {
  name: string
  type: ColumnType
  options?: string[] // For select type
  referenceSheet?: string // For reference type
}

export interface DatabaseRow {
  id: string
  [key: string]: any
  __comments__?: Comment[]
  __activity__?: ActivityLogEntry[]
}

export interface Database {
  name: string
  path: string
  columns: ColumnDefinition[]
  rows: DatabaseRow[]
}

// Comment system
export interface Comment {
  id: string
  content: string
  author: string
  timestamp: Date
  parentId?: string // For threaded comments
}

// Activity log
export interface ActivityLogEntry {
  id: string
  event: string
  field?: string
  oldValue?: any
  newValue?: any
  timestamp: Date
  user: string
}

// Page types
export type PageType = 'markdown' | 'database'

export interface Page {
  id: string
  name: string
  path: string
  type: PageType
  lastModified: Date
  parentId?: string // For nested pages
}

// UI state types
export interface AppState {
  currentVault: Vault | null
  selectedFile: string | null
  sidebarCollapsed: boolean
  theme: 'light' | 'dark'
}

// Editor types
export interface MarkdownFile {
  path: string
  content: string
  lastModified: Date
}

export interface EditorState {
  mode: 'edit' | 'preview'
  content: string
  isDirty: boolean
}

// Settings
export interface AppSettings {
  theme: 'light' | 'dark' | 'system'
  defaultVaultPath: string
  fontSize: number
  autoSave: boolean
  showLineNumbers: boolean
}

// Error types
export interface AppError {
  message: string
  code?: string
  details?: any
}
