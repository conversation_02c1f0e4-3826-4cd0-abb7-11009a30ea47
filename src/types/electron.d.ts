export interface ElectronAPI {
  selectDirectory: () => Promise<string | null>
  readDirectory: (dirPath: string) => Promise<Array<{
    name: string
    isDirectory: boolean
    path: string
  }>>
  readFile: (filePath: string) => Promise<string>
  writeFile: (filePath: string, content: string) => Promise<boolean>
  createDirectory: (dirPath: string) => Promise<boolean>
  deleteFile: (filePath: string) => Promise<boolean>
  deleteDirectory: (dirPath: string) => Promise<boolean>
  moveFile: (sourcePath: string, targetPath: string) => Promise<boolean>
  onMenuNewVault: (callback: () => void) => void
  onMenuOpenVault: (callback: () => void) => void
  onMenuNewNote: (callback: () => void) => void
  onMenuNewDatabase: (callback: () => void) => void
  removeAllListeners: (channel: string) => void
}

declare global {
  interface Window {
    electronAPI: ElectronAPI
  }
}
