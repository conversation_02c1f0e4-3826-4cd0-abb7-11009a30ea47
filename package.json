{"name": "ka<PERSON><PERSON>hq", "version": "0.1.0", "description": "A desktop application inspired by Obsidian and Notion - local-first markdown vault with flexible database features", "main": "dist/main.js", "homepage": "./", "private": true, "scripts": {"dev": "concurrently \"npm run dev:renderer\" \"npm run dev:main\"", "dev:renderer": "vite", "dev:main": "tsc -p tsconfig.main.json && electron dist/main.js --dev", "build": "npm run build:renderer && npm run build:main", "build:renderer": "vite build", "build:main": "tsc -p tsconfig.main.json", "preview": "vite preview", "pack": "electron-builder --dir", "dist": "npm run build && electron-builder", "type-check": "tsc --noEmit", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@codemirror/autocomplete": "^6.18.6", "@codemirror/commands": "^6.8.1", "@codemirror/highlight": "^0.19.8", "@codemirror/lang-markdown": "^6.3.2", "@codemirror/language": "^6.11.0", "@codemirror/lint": "^6.8.5", "@codemirror/search": "^6.5.11", "@codemirror/state": "^6.5.2", "@codemirror/theme-one-dark": "^6.1.2", "@codemirror/view": "^6.36.8", "@lezer/highlight": "^1.2.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "electron-squirrel-startup": "^1.0.1", "lucide-react": "^0.294.0", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "concurrently": "^8.2.2", "electron": "^27.1.2", "electron-builder": "^24.6.4", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0", "vite-plugin-electron": "^0.15.5"}, "build": {"appId": "com.kapturehq.app", "productName": "KaptureHQ", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*"], "mac": {"category": "public.app-category.productivity"}, "win": {"target": "nsis"}, "linux": {"target": "AppImage"}}, "keywords": ["electron", "react", "typescript", "obsidian", "notion", "markdown", "notes", "database"], "author": "KaptureHQ Team", "license": "MIT"}